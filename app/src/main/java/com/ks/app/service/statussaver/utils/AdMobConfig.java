package com.ks.app.service.statussaver.utils;

import android.content.Context;
import android.util.Log;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.RequestConfiguration;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import java.util.Arrays;

/**
 * ✅ AdMob Configuration and Management
 * Handles AdMob initialization, native ads, and interstitial ads
 * Following Google AdMob Android Quick Start Guide best practices
 */
public class AdMobConfig {

    private static final String TAG = "AdMobConfig";
    
    // ✅ Production Ad Unit IDs for SavePro – Status Downloader
    public static class AdUnitIds {
        // Interstitial Ad Unit ID (After Splash - First User Action)
        public static final String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-7557152164205920/6418571631";

        // Native Ad Unit ID (Home, Image Preview, Video Preview)
        public static final String NATIVE_AD_UNIT_ID = "ca-app-pub-7557152164205920/5890575132";
    }
    
    // ✅ AdMob Initialization Status
    private static boolean isInitialized = false;
    private static InterstitialAd interstitialAd;

    // ✅ Ad Frequency Control
    private static long lastInterstitialShowTime = 0;
    private static final long INTERSTITIAL_COOLDOWN_MS = 60000; // 1 minute between interstitials
    
    /**
     * ✅ Initialize AdMob SDK
     * Call this in your Application class or main activity
     */
    public static void initialize(Context context) {
        if (isInitialized) {
            Log.d(TAG, "AdMob already initialized");
            return;
        }
        
        Log.d(TAG, "Initializing AdMob SDK...");
        
        // ✅ Configure test devices for development
        RequestConfiguration configuration = new RequestConfiguration.Builder()
                .setTestDeviceIds(Arrays.asList("ABCDEF012345"))  // Add your test device ID
                .build();
        MobileAds.setRequestConfiguration(configuration);
        
        // ✅ Initialize Mobile Ads SDK
        MobileAds.initialize(context, new OnInitializationCompleteListener() {
            @Override
            public void onInitializationComplete(InitializationStatus initializationStatus) {
                Log.d(TAG, "AdMob SDK initialized successfully");
                Log.d(TAG, "Initialization status: " + initializationStatus.toString());
                isInitialized = true;
            }
        });
    }
    
    /**
     * ✅ Create optimized AdRequest
     * Returns a properly configured AdRequest for better ad performance
     */
    public static AdRequest createAdRequest() {
        return new AdRequest.Builder()
                .build();
    }
    

    
    /**
     * ✅ Load Interstitial Ad
     * Loads an interstitial ad for later display
     * Following latest Google AdMob Interstitial Ad guidelines
     */
    public static void loadInterstitialAd(Context context) {
        Log.d(TAG, "Loading interstitial ad...");

        AdRequest adRequest = createAdRequest();

        InterstitialAd.load(context, AdUnitIds.INTERSTITIAL_AD_UNIT_ID, adRequest,
                new InterstitialAdLoadCallback() {
                    @Override
                    public void onAdLoaded(InterstitialAd ad) {
                        Log.d(TAG, "Interstitial ad loaded successfully");
                        interstitialAd = ad;
                        PerformanceMonitor.logAdLoadSuccess("Interstitial");

                        // Set up full screen content callback
                        setupInterstitialCallbacks(ad);
                    }

                    @Override
                    public void onAdFailedToLoad(LoadAdError loadAdError) {
                        Log.w(TAG, "Interstitial ad failed to load: " + loadAdError.getMessage());
                        Log.w(TAG, "Error code: " + loadAdError.getCode());
                        Log.w(TAG, "Error domain: " + loadAdError.getDomain());
                        interstitialAd = null;
                        PerformanceMonitor.logAdLoadFailure("Interstitial", loadAdError.getMessage());
                    }
                });
    }
    
    /**
     * ✅ Setup Interstitial Ad Callbacks
     * Sets up full screen content callback for better user experience
     */
    private static void setupInterstitialCallbacks(InterstitialAd ad) {
        ad.setFullScreenContentCallback(new com.google.android.gms.ads.FullScreenContentCallback() {
            @Override
            public void onAdClicked() {
                Log.d(TAG, "Interstitial ad was clicked");
                PerformanceMonitor.logAdShow("Interstitial - Clicked");
            }

            @Override
            public void onAdDismissedFullScreenContent() {
                Log.d(TAG, "Interstitial ad dismissed");
                interstitialAd = null;
                PerformanceMonitor.logAdShow("Interstitial - Dismissed");
            }

            @Override
            public void onAdFailedToShowFullScreenContent(com.google.android.gms.ads.AdError adError) {
                Log.w(TAG, "Interstitial ad failed to show: " + adError.getMessage());
                interstitialAd = null;
                PerformanceMonitor.logAdLoadFailure("Interstitial Show", adError.getMessage());
            }

            @Override
            public void onAdImpression() {
                Log.d(TAG, "Interstitial ad recorded an impression");
                PerformanceMonitor.logAdShow("Interstitial - Impression");
            }

            @Override
            public void onAdShowedFullScreenContent() {
                Log.d(TAG, "Interstitial ad showed full screen content");
                PerformanceMonitor.logAdShow("Interstitial - Showed");
            }
        });
    }

    /**
     * ✅ Check if Interstitial Ad Can Be Shown
     * Implements frequency capping to prevent ad spam
     */
    public static boolean canShowInterstitialAd() {
        long currentTime = System.currentTimeMillis();
        boolean cooldownPassed = (currentTime - lastInterstitialShowTime) >= INTERSTITIAL_COOLDOWN_MS;
        boolean adReady = interstitialAd != null;

        Log.d(TAG, "Can show interstitial: cooldown=" + cooldownPassed + ", ready=" + adReady);
        return cooldownPassed && adReady;
    }

    /**
     * ✅ Show Interstitial Ad
     * Shows the loaded interstitial ad if available and cooldown has passed
     * Following latest Google AdMob best practices with frequency capping
     */
    public static void showInterstitialAd(android.app.Activity activity) {
        if (canShowInterstitialAd()) {
            Log.d(TAG, "Showing interstitial ad");
            lastInterstitialShowTime = System.currentTimeMillis();
            interstitialAd.show(activity);
            // Note: interstitialAd will be set to null in onAdDismissedFullScreenContent callback
        } else if (interstitialAd == null) {
            Log.w(TAG, "Interstitial ad not ready");
        } else {
            Log.w(TAG, "Interstitial ad cooldown active - please wait");
        }
    }
    
    /**
     * ✅ Check if Interstitial Ad is Ready
     * Returns true if an interstitial ad is loaded and ready to show
     */
    public static boolean isInterstitialAdReady() {
        return interstitialAd != null;
    }

    /**
     * ✅ Show Interstitial Ad with Auto-Reload
     * Shows interstitial ad and automatically loads the next one
     * Best practice for continuous ad serving
     */
    public static void showInterstitialAdWithReload(android.app.Activity activity) {
        if (interstitialAd != null) {
            Log.d(TAG, "Showing interstitial ad with auto-reload");

            // Set up callback to reload after dismissal
            interstitialAd.setFullScreenContentCallback(new com.google.android.gms.ads.FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    Log.d(TAG, "Interstitial ad dismissed - loading next ad");
                    interstitialAd = null;
                    // Automatically load the next interstitial ad
                    loadInterstitialAd(activity);
                    PerformanceMonitor.logAdShow("Interstitial - Auto-Reload");
                }

                @Override
                public void onAdFailedToShowFullScreenContent(com.google.android.gms.ads.AdError adError) {
                    Log.w(TAG, "Interstitial ad failed to show: " + adError.getMessage());
                    interstitialAd = null;
                    // Try to load a new ad even if showing failed
                    loadInterstitialAd(activity);
                    PerformanceMonitor.logAdLoadFailure("Interstitial Auto-Reload", adError.getMessage());
                }

                @Override
                public void onAdShowedFullScreenContent() {
                    Log.d(TAG, "Interstitial ad showed full screen content");
                    PerformanceMonitor.logAdShow("Interstitial - Auto-Reload Shown");
                }
            });

            interstitialAd.show(activity);
        } else {
            Log.w(TAG, "Interstitial ad not ready - loading new ad");
            loadInterstitialAd(activity);
        }
    }
    

    
    /**
     * ✅ AdMob Configuration Helper
     * Utility methods for AdMob configuration
     */
    public static class ConfigHelper {
        
        /**
         * Check if AdMob is initialized
         */
        public static boolean isInitialized() {
            return AdMobConfig.isInitialized;
        }
        
        /**
         * Get AdMob SDK version info
         */
        public static String getSDKVersionInfo() {
            return "Google Mobile Ads SDK v23.0.0";
        }
        
        /**
         * Enable test mode for development
         */
        public static void enableTestMode(Context context) {
            RequestConfiguration configuration = new RequestConfiguration.Builder()
                    .setTestDeviceIds(Arrays.asList(
                            "ABCDEF012345",  // Add your test device IDs here
                            "TEST_DEVICE_HASHED_ID"
                    ))
                    .build();
            MobileAds.setRequestConfiguration(configuration);
            Log.d(TAG, "AdMob test mode enabled");
        }
    }
    
    /**
     * ✅ Ad Performance Monitoring
     * Helper methods for monitoring ad performance
     */
    public static class PerformanceMonitor {
        
        /**
         * Log ad load success
         */
        public static void logAdLoadSuccess(String adType) {
            Log.i(TAG, "✅ " + adType + " ad loaded successfully");
        }
        
        /**
         * Log ad load failure
         */
        public static void logAdLoadFailure(String adType, String error) {
            Log.w(TAG, "❌ " + adType + " ad failed to load: " + error);
        }
        
        /**
         * Log ad show event
         */
        public static void logAdShow(String adType) {
            Log.i(TAG, "📺 " + adType + " ad shown to user");
        }
    }
}
