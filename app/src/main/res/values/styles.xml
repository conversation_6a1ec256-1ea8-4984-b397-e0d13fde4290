<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="WhitePopupMenuTextAppearance" parent="TextAppearance.AppCompat.Widget.PopupMenu.Large">
        <item name="android:textColor">@android:color/white</item>
    </style>
    <style name="WhitePopupMenuTextAppearanceSmall" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small">
        <item name="android:textColor">@android:color/white</item>
    </style>
    <style name="WhitePopupMenuText" parent="Widget.AppCompat.PopupMenu">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textAppearance">@style/WhitePopupMenuTextAppearance</item>
        <item name="android:textAppearanceSmall">@style/WhitePopupMenuTextAppearanceSmall</item>
    </style>
    <style name="AppTextWhite" parent="TextAppearance.AppCompat">
        <item name="android:textColor">@android:color/white</item>
    </style>

    <!-- Tab text appearance for icons and text -->
    <style name="TabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
        <item name="android:textSize">11sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginStart">4dp</item>
    </style>

    <!-- Custom TabLayout style for better alignment -->
    <style name="CustomTabLayoutStyle" parent="Widget.MaterialComponents.TabLayout">
        <item name="tabIconTint">?attr/colorOnSurface</item>
        <item name="tabTextAppearance">@style/TabTextAppearance</item>
        <item name="tabInlineLabel">true</item>
        <item name="tabMode">fixed</item>
        <item name="tabGravity">fill</item>
        <item name="tabMinWidth">0dp</item>
        <item name="tabMaxWidth">0dp</item>
        <item name="tabPaddingStart">4dp</item>
        <item name="tabPaddingEnd">4dp</item>
        <item name="tabPaddingTop">8dp</item>
        <item name="tabPaddingBottom">8dp</item>
        <item name="tabIconTintMode">src_in</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="Theme.SmartStatusSaver.Splash" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:windowBackground">@color/colorPrimary</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
        <item name="android:navigationBarColor">@color/colorPrimary</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>

    <!-- Permission Bottom Sheet Dialog Theme -->
    <style name="Theme.PermissionBottomSheetDialog" parent="Theme.MaterialComponents.DayNight.BottomSheetDialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackground">#1E1E1E</item>
        <item name="android:backgroundDimAmount">0.5</item>
        <item name="android:windowIsFloating">false</item>
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
    </style>

    <!-- Custom Bottom Sheet Style -->
    <style name="CustomBottomSheetStyle" parent="Widget.MaterialComponents.BottomSheet">
        <item name="android:background">@drawable/permission_dialog_background</item>
    </style>

</resources>