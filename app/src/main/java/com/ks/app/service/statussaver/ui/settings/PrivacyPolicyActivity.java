package com.ks.app.service.statussaver.ui.settings;

import android.os.Bundle;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.ks.app.service.statussaver.R;

/**
 * Privacy Policy Activity
 */
public class PrivacyPolicyActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        setTheme(R.style.Theme_SettingsStatusBar);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_privacy_policy);

        setupBackButton();
        setupContent();
    }

    private void setupBackButton() {
        ImageView backIcon = findViewById(R.id.back_icon);
        backIcon.setImageResource(R.drawable.ic_arrow_back_24);
        backIcon.setOnClickListener(v -> finish());
    }

    private void setupContent() {
        TextView contentText = findViewById(R.id.content_text);

        String privacyContent = "Effective Date: June 1, 2025\n" +
                "App Name: SavePro – Status Downloader\n" +
                "Developer Contact: 📧 <EMAIL>\n\n" +

                "1. Introduction\n\n" +
                "At SavePro – Status Downloader (\"we,\" \"us,\" or \"our\"), your privacy is a priority. This Privacy Policy explains how we collect, use, disclose, and protect your information when you use our mobile application (\"Application\"). By accessing or using the Application, you agree to the practices described in this Policy.\n\n" +

                "2. Information We Collect\n\n" +
                "We prioritize user privacy by collecting only essential, non-personally identifiable information to improve the Application's performance and functionality. We do not collect personal data such as your name, email, contacts, or precise location.\n\n" +
                "Types of information collected:\n\n" +
                "Device Information: Operating system version, device model, and other non-identifiable technical data.\n\n" +
                "Usage Data: Pages/screens viewed, time spent in the app, app version, crash logs, and performance diagnostics.\n\n" +

                "3. Permissions and Access\n\n" +
                "Our Application requests permission to access your device's local media files solely to enable the core functionality of viewing and saving status videos and images. This access is strictly local, and no media files are uploaded or transmitted.\n\n" +
                "Android 10 and below: READ_EXTERNAL_STORAGE to read local status files.\n\n" +
                "Android 13 and above: READ_MEDIA_IMAGES and READ_MEDIA_VIDEO for image and video access.\n\n" +
                "Important: We never collect, transmit, or sell your personal media or files.\n\n" +

                "4. How We Use Your Information\n\n" +
                "The data we collect is used exclusively for:\n\n" +
                "Enhancing app performance and stability.\n\n" +
                "Diagnosing and fixing technical issues.\n\n" +
                "Delivering in-app notifications about important updates.\n\n" +
                "We do not use your information for advertising, marketing, profiling, or third-party tracking.\n\n" +

                "5. Data Sharing and Disclosure\n\n" +
                "We do not sell, rent, or trade your information. We may share your data only in the following limited circumstances:\n\n" +
                "With trusted service providers under strict confidentiality agreements to help us maintain and improve the Application.\n\n" +
                "When legally required by law enforcement, court orders, or to protect against fraud or abuse.\n\n" +

                "6. Data Retention and Control\n\n" +
                "We retain diagnostic and usage data only as long as necessary to improve the Application. Media files remain on your device and are never stored on our servers. You may revoke media access permissions at any time via your device settings.\n\n" +
                "To request deletion of diagnostic data or crash logs, please contact us at 📧 <EMAIL>.\n\n" +

                "7. Children's Privacy\n\n" +
                "Our Application is not intended for children under 13 years old. We do not knowingly collect any personal information from children under 13. If we learn that such data has been collected, we will delete it promptly.\n\n" +

                "8. Security Measures\n\n" +
                "We employ industry-standard security protocols to safeguard your information. However, no method of transmission or storage is 100% secure, and we cannot guarantee absolute security.\n\n" +

                "9. International Users\n\n" +
                "If you are accessing our Application from outside your home country, please be aware that your information may be processed in accordance with this Privacy Policy and applicable local laws.\n\n" +

                "10. Changes to This Privacy Policy\n\n" +
                "We may update this Privacy Policy to reflect changes in our practices or legal requirements. We will notify users by updating the Effective Date and posting changes within the Application or on our website.\n\n" +

                "11. Your Rights and Choices\n\n" +
                "Depending on your location, you may have rights under applicable data protection laws, including the right to access, correct, or delete your data. Although we collect minimal personal information, you can manage permissions and control data collection through your device settings.\n\n" +

                "12. Contact Us\n\n" +
                "If you have any questions or concerns about this Privacy Policy or our privacy practices, please contact us at:\n" +
                "📧 <EMAIL>";

        contentText.setText(privacyContent);
    }
}
