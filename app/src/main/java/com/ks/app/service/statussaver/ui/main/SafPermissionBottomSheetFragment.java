package com.ks.app.service.statussaver.ui.main;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.ks.app.service.statussaver.R;

public class SafPermissionBottomSheetFragment extends BottomSheetDialogFragment {

    public static final String TAG = "AccessPermissionBottomSheetFragment";

    private AccessPermissionListener listener;

    public interface AccessPermissionListener {
        void onAllowAccess();
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof AccessPermissionListener) {
            listener = (AccessPermissionListener) context;
        } else {
            throw new ClassCastException(context.toString() + " must implement AccessPermissionListener");
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // Set custom dark theme for the permission bottom sheet dialog
        setStyle(STYLE_NORMAL, R.style.Theme_PermissionBottomSheetDialog);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.bottom_sheet_saf_permission, container, false);

        Button allowButton = view.findViewById(R.id.button_allow_access);

        allowButton.setOnClickListener(v -> {
            if (listener != null) {
                listener.onAllowAccess();
            }
            dismiss();
        });

        return view;
    }
} 