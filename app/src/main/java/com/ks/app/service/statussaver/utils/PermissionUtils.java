package com.ks.app.service.statussaver.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import java.util.ArrayList;
import java.util.List;

/**
 * Optimized Permission Manager for WhatsApp Status Saver (2025)
 * ✅ Supports Android 13+ (Tiramisu) granular media permissions
 * ✅ Handles scoped storage properly
 * ✅ No deprecated permissions
 * ✅ Play Store compliant
 */
public class PermissionUtils {

    private static final String TAG = "PermissionUtils";
    public static final int PERMISSIONS_REQUEST_CODE = 101;
    private static boolean isRequestingPermissions = false;

    /**
     * Get required permissions based on Android version
     * Android 13+: READ_MEDIA_IMAGES, READ_MEDIA_VIDEO
     * Android 10-12: READ_EXTERNAL_STORAGE only (scoped storage)
     * Android 9-: READ_EXTERNAL_STORAGE, WRITE_EXTERNAL_STORAGE
     */
    private static String[] getRequiredPermissions() {
        List<String> permissions = new ArrayList<>();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ (API 33+) - Granular media permissions
            permissions.add(Manifest.permission.READ_MEDIA_IMAGES);
            permissions.add(Manifest.permission.READ_MEDIA_VIDEO);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10-12 (API 29-32) - Scoped storage era
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
            // Note: WRITE_EXTERNAL_STORAGE not needed for scoped storage
        } else {
            // Android 9 and below (API 28-) - Legacy storage
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
            permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }

        return permissions.toArray(new String[0]);
    }

    /**
     * Check if all required permissions are granted
     * Safe to call from onResume() or any lifecycle method
     */
    public static boolean hasPermissions(Context context) {
        try {
            if (context == null) {
                Log.e(TAG, "Context is null in hasPermissions");
                return false;
            }

            String[] requiredPermissions = null;
            try {
                requiredPermissions = getRequiredPermissions();
            } catch (Exception e) {
                Log.e(TAG, "Error getting required permissions: " + e.getMessage(), e);
                return false;
            }

            // If no permissions required for this Android version, return true
            if (requiredPermissions == null || requiredPermissions.length == 0) {
                return true;
            }

            // Check each required permission
            for (String permission : requiredPermissions) {
                if (permission != null) {
                    try {
                        int permissionStatus = ContextCompat.checkSelfPermission(context, permission);
                        if (permissionStatus != PackageManager.PERMISSION_GRANTED) {
                            return false;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking permission " + permission + ": " + e.getMessage(), e);
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error in hasPermissions: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * Request missing permissions
     * Prevents duplicate requests and handles all Android versions
     */
    public static void requestPermissions(Activity activity) {
        try {
            if (activity == null) {
                Log.e(TAG, "Activity is null in requestPermissions");
                return;
            }

            // Prevent multiple simultaneous requests
            if (isRequestingPermissions) {
                return;
            }

            String[] requiredPermissions = null;
            try {
                requiredPermissions = getRequiredPermissions();
            } catch (Exception e) {
                Log.e(TAG, "Error getting required permissions for request: " + e.getMessage(), e);
                return;
            }

            if (requiredPermissions == null) {
                Log.e(TAG, "Required permissions is null");
                return;
            }

            List<String> permissionsToRequest = new ArrayList<>();

            // Find missing permissions
            for (String permission : requiredPermissions) {
                if (permission != null) {
                    try {
                        int permissionStatus = ContextCompat.checkSelfPermission(activity, permission);
                        if (permissionStatus != PackageManager.PERMISSION_GRANTED) {
                            permissionsToRequest.add(permission);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking permission " + permission + " for request: " + e.getMessage(), e);
                    }
                }
            }

            // Request missing permissions
            if (!permissionsToRequest.isEmpty()) {
                try {
                    isRequestingPermissions = true;
                    ActivityCompat.requestPermissions(
                        activity,
                        permissionsToRequest.toArray(new String[0]),
                        PERMISSIONS_REQUEST_CODE
                    );
                } catch (Exception e) {
                    Log.e(TAG, "Error requesting permissions: " + e.getMessage(), e);
                    isRequestingPermissions = false; // Reset flag on error
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in requestPermissions: " + e.getMessage(), e);
            isRequestingPermissions = false; // Reset flag on error
        }
    }

    // Call this from Activity's onRequestPermissionsResult
    public static void onRequestPermissionsResultReceived() {
        try {
            isRequestingPermissions = false;
        } catch (Exception e) {
            Log.e(TAG, "Error in onRequestPermissionsResultReceived: " + e.getMessage(), e);
        }
    }

    public static boolean isPermissionRequestInProgress(Context context) { // Context not strictly needed but good for consistency
        try {
            return isRequestingPermissions;
        } catch (Exception e) {
            Log.e(TAG, "Error in isPermissionRequestInProgress: " + e.getMessage(), e);
            return false;
        }
    }

    // It's good practice to also include a method to check if we should show a rationale
    public static boolean shouldShowRationale(Activity activity) {
        try {
            if (activity == null) {
                Log.e(TAG, "Activity is null in shouldShowRationale");
                return false;
            }

            String[] requiredPermissions = null;
            try {
                requiredPermissions = getRequiredPermissions();
            } catch (Exception e) {
                Log.e(TAG, "Error getting required permissions for rationale: " + e.getMessage(), e);
                return false;
            }

            if (requiredPermissions == null) {
                return false;
            }

            for (String permission : requiredPermissions) {
                if (permission != null) {
                    try {
                        if (ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)) {
                            return true;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking rationale for permission " + permission + ": " + e.getMessage(), e);
                    }
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error in shouldShowRationale: " + e.getMessage(), e);
            return false;
        }
    }
}