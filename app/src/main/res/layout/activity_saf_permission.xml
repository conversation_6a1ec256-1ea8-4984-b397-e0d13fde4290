<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center_horizontal">

        <!-- Status Bar Spacer -->
        <View
            android:layout_width="match_parent"
            android:layout_height="48dp" />

        <!-- Title -->
        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Media Access Permission"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <!-- Subtitle -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Grant access to view and save WhatsApp statuses"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="#B3FFFFFF"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- Visual Guide Container with Shimmer -->
        <com.facebook.shimmer.ShimmerFrameLayout
            android:id="@+id/shimmer_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:shimmer_duration="1500"
            app:shimmer_auto_start="true"
            app:shimmer_direction="left_to_right"
            app:shimmer_repeat_count="infinite"
            app:shimmer_repeat_mode="restart">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="#1A1A1A"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Guide Title -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Navigate to com.whatsapp folder:"
                    android:textColor="@android:color/white"
                    android:textStyle="bold"
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp" />

                <!-- Folder Structure Visual Guide -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="#0D1117"
                    android:padding="12dp"
                    android:layout_marginBottom="12dp">

                    <!-- Breadcrumb Path -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="POCO X4 Pro 5G"
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" > "
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Android"
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" > "
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="media"
                            android:textColor="#58A6FF"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- Files in media Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Files in media"
                            android:textColor="@android:color/white"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="1dp"
                            android:layout_weight="1" />

                        <!-- Grid icon -->
                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_grid_view"
                            android:tint="#8B949E" />

                    </LinearLayout>

                    <!-- App Folder Items -->
                    <ScrollView
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginBottom="12dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <!-- com.huami.watch.pop -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="com.huami.watch.pop"
                                    android:textColor="@android:color/white"
                                    android:textSize="14sp" />

                            </LinearLayout>

                            <!-- com.instagram.android -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="com.instagram.android"
                                    android:textColor="@android:color/white"
                                    android:textSize="14sp" />

                            </LinearLayout>

                            <!-- com.naing.mp3converter -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="com.naing.mp3converter"
                                    android:textColor="@android:color/white"
                                    android:textSize="14sp" />

                            </LinearLayout>

                            <!-- com.openai.chatgpt -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="com.openai.chatgpt"
                                    android:textColor="@android:color/white"
                                    android:textSize="14sp" />

                            </LinearLayout>

                            <!-- com.save.status.photos.videos.saver -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="com.save.status.photos.videos.saver"
                                    android:textColor="@android:color/white"
                                    android:textSize="14sp" />

                            </LinearLayout>

                            <!-- com.whatsapp (Highlighted) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#2D3748">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#58A6FF"
                                    android:layout_marginEnd="12dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="com.whatsapp"
                                    android:textColor="@android:color/white"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <!-- com.xiaomi.bluetooth -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="com.xiaomi.bluetooth"
                                    android:textColor="@android:color/white"
                                    android:textSize="14sp" />

                            </LinearLayout>

                        </LinearLayout>

                    </ScrollView>

                </LinearLayout>

                <!-- Use This Folder Button (Visual Guide) -->
                <Button
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:text="USE THIS FOLDER"
                    android:textColor="#000000"
                    android:textStyle="bold"
                    android:background="#E6E6E6"
                    android:enabled="false"
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Instructions -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="After clicking 'Grant Access', navigate to:\nAndroid → media → com.whatsapp\nThen tap 'USE THIS FOLDER'"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="#B3FFFFFF"
            android:gravity="center"
            android:lineSpacingExtra="4dp"
            android:layout_marginBottom="24dp" />

        <!-- Spacer to push button to bottom -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- Grant Access Button -->
        <Button
            android:id="@+id/button_allow_access"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="Grant Access"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            android:textSize="16sp"
            android:background="@drawable/cta_button_background"
            android:elevation="4dp"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp" />

    </LinearLayout>

</ScrollView>
