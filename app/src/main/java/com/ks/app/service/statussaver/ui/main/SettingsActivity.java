package com.ks.app.service.statussaver.ui.main;

import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ListView;
import android.widget.Toast;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import com.ks.app.service.statussaver.R;
import android.widget.TextView;
import android.widget.ImageView;
import android.os.Build;
import android.content.Intent;

public class SettingsActivity extends AppCompatActivity {
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        setTheme(R.style.Theme_SettingsStatusBar);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        ListView listView = findViewById(R.id.settings_list);
        String[] items = {"Privacy Policy", "Contact Us", "About"};
        ArrayAdapter<String> adapter = new ArrayAdapter<String>(this, android.R.layout.simple_list_item_1, items) {
            @Override
            public View getView(int position, View convertView, android.view.ViewGroup parent) {
                View view = super.getView(position, convertView, parent);
                TextView text = view.findViewById(android.R.id.text1);
                text.setTextColor(getResources().getColor(android.R.color.white));
                return view;
            }
        };
        listView.setAdapter(adapter);

        listView.setOnItemClickListener((parent, view, position, id) -> {
            Intent intent;
            switch (position) {
                case 0: // Privacy Policy
                    intent = new Intent(this, com.ks.app.service.statussaver.ui.settings.PrivacyPolicyActivity.class);
                    startActivity(intent);
                    break;
                case 1: // Contact Us
                    intent = new Intent(this, com.ks.app.service.statussaver.ui.settings.ContactUsActivity.class);
                    startActivity(intent);
                    break;
                case 2: // About
                    intent = new Intent(this, com.ks.app.service.statussaver.ui.settings.AboutActivity.class);
                    startActivity(intent);
                    break;
            }
        });

        ImageView backIcon = findViewById(R.id.back_icon);
        backIcon.setImageResource(R.drawable.ic_arrow_back_24);
        backIcon.setOnClickListener(v -> finish());
    }
} 