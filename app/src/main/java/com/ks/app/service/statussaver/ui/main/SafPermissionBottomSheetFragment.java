package com.ks.app.service.statussaver.ui.main;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.animation.ObjectAnimator;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.ks.app.service.statussaver.R;

public class SafPermissionBottomSheetFragment extends BottomSheetDialogFragment {

    public static final String TAG = "AccessPermissionBottomSheetFragment";

    private AccessPermissionListener listener;
    private ImageView handClickAnimation;
    private Handler animationHandler;
    private Runnable handClickRunnable;

    public interface AccessPermissionListener {
        void onAllowAccess();
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof AccessPermissionListener) {
            listener = (AccessPermissionListener) context;
        } else {
            throw new ClassCastException(context.toString() + " must implement AccessPermissionListener");
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // Set custom dark theme for full screen permission dialog
        setStyle(STYLE_NORMAL, R.style.Theme_PermissionBottomSheetDialog);
        animationHandler = new Handler(Looper.getMainLooper());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.bottom_sheet_saf_permission, container, false);

        // Initialize views
        Button allowButton = view.findViewById(R.id.button_allow_access);
        handClickAnimation = view.findViewById(R.id.hand_click_animation);

        // Setup button click listener
        allowButton.setOnClickListener(v -> {
            if (listener != null) {
                listener.onAllowAccess();
            }
            dismiss();
        });

        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Make bottom sheet full screen
        setupFullScreenBottomSheet();

        // Start hand click animation
        startHandClickAnimation();
    }

    /**
     * ✅ Setup Full Screen Bottom Sheet
     */
    private void setupFullScreenBottomSheet() {
        if (getDialog() instanceof BottomSheetDialog) {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) getDialog();
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);

            if (bottomSheet != null) {
                BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(bottomSheet);
                behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                behavior.setSkipCollapsed(true);
                behavior.setHideable(true);

                // Set full height
                ViewGroup.LayoutParams layoutParams = bottomSheet.getLayoutParams();
                layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT;
                bottomSheet.setLayoutParams(layoutParams);
            }
        }
    }

    /**
     * ✅ Start Hand Click Animation on USE THIS FOLDER button
     */
    private void startHandClickAnimation() {
        if (handClickAnimation != null) {
            // Show hand click animation after 2 seconds
            handClickRunnable = () -> {
                handClickAnimation.setVisibility(View.VISIBLE);

                // Create pulsing click animation
                ObjectAnimator scaleX = ObjectAnimator.ofFloat(handClickAnimation, "scaleX", 1.0f, 1.3f, 1.0f);
                ObjectAnimator scaleY = ObjectAnimator.ofFloat(handClickAnimation, "scaleY", 1.0f, 1.3f, 1.0f);
                ObjectAnimator alpha = ObjectAnimator.ofFloat(handClickAnimation, "alpha", 0.9f, 0.5f, 0.9f);

                // Set repeat count on individual animators
                scaleX.setRepeatCount(ValueAnimator.INFINITE);
                scaleY.setRepeatCount(ValueAnimator.INFINITE);
                alpha.setRepeatCount(ValueAnimator.INFINITE);

                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playTogether(scaleX, scaleY, alpha);
                animatorSet.setDuration(1000); // Faster click animation
                animatorSet.start();
            };

            animationHandler.postDelayed(handClickRunnable, 2000);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // Clean up animation handler
        if (animationHandler != null && handClickRunnable != null) {
            animationHandler.removeCallbacks(handClickRunnable);
        }
    }

}