package com.ks.app.service.statussaver.ui.splash;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.ui.main.MainActivity;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.AdError;
import androidx.annotation.NonNull;

public class SplashActivity extends AppCompatActivity {

    private static final String TAG = "SplashActivity";
    private static final int SPLASH_DURATION = 3000; // 3 seconds - scalable approach
    private static final int MAX_AD_WAIT_TIME = 5000; // Maximum 5 seconds to wait for ad

    // Your production interstitial ad unit ID
    private static final String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-7557152164205920/6418571631";

    private InterstitialAd mInterstitialAd;
    private boolean splashTimerFinished = false;
    private boolean adLoaded = false;
    private boolean adShown = false;
    private long adLoadStartTime = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        // Hide the action bar for full screen splash
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        // Initialize views
        ImageView splashIcon = findViewById(R.id.splash_icon);
        TextView appName = findViewById(R.id.splash_app_name);
        ProgressBar loadingIndicator = findViewById(R.id.splash_loading_indicator);

        // Create fade-in animation
        AlphaAnimation fadeIn = new AlphaAnimation(0.0f, 1.0f);
        fadeIn.setDuration(1000); // 1 second fade-in
        fadeIn.setStartOffset(200); // Start after 200ms

        // Apply animations
        splashIcon.startAnimation(fadeIn);
        appName.startAnimation(fadeIn);
        loadingIndicator.startAnimation(fadeIn);

        Log.d(TAG, "🚀 SCALABLE APPROACH: Starting 3-second splash with background ad loading");

        // ✅ Initialize Mobile Ads SDK immediately for background loading
        MobileAds.initialize(this, initializationStatus -> {
            Log.d(TAG, "✅ AdMob SDK initialized - ready for background ad loading");
        });

        // ✅ Start loading interstitial ad in background immediately
        Log.d(TAG, "🔄 Starting background interstitial ad loading during 3-second splash...");
        loadInterstitialAdInBackground();

        // ✅ SCALABLE APPROACH: Wait 3 seconds, then check if ad is loaded
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "⏰ SCALABLE CHECK: 3 seconds completed - checking if ad is loaded");
                splashTimerFinished = true;
                checkAdStatusAndProceed();
            }
        }, SPLASH_DURATION);
    }

    /**
     * ✅ Load Interstitial Ad in Background - Optimized for Splash Screen
     * Starts loading immediately when splash screen appears for faster ad delivery
     */
    private void loadInterstitialAdInBackground() {
        adLoadStartTime = System.currentTimeMillis();
        Log.d(TAG, "🚀 BACKGROUND LOADING: Starting interstitial ad load during splash screen");
        Log.d(TAG, "📱 Ad Unit ID: " + INTERSTITIAL_AD_UNIT_ID);
        Log.d(TAG, "⏰ Load start time: " + adLoadStartTime);

        // Create ad request
        AdRequest adRequest = new AdRequest.Builder().build();

        // Start loading ad in background
        InterstitialAd.load(this, INTERSTITIAL_AD_UNIT_ID, adRequest,
            new InterstitialAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.d(TAG, "✅ SCALABLE SUCCESS: Interstitial ad loaded during background loading!");
                    Log.d(TAG, "⚡ Load time: " + loadTime + "ms (Target: within " + SPLASH_DURATION + "ms)");

                    mInterstitialAd = interstitialAd;
                    adLoaded = true;

                    if (loadTime < SPLASH_DURATION) {
                        Log.d(TAG, "🚀 PERFECT TIMING: Ad loaded in " + loadTime + "ms (within 3-second window)");
                        Log.d(TAG, "⏳ Waiting for 3-second timer to complete...");
                    } else {
                        Log.d(TAG, "⏰ SLOW LOADING: Ad took " + loadTime + "ms (longer than 3 seconds)");
                        Log.d(TAG, "🎯 Will proceed immediately when timer finishes");
                    }

                    // Check if 3-second timer has finished
                    if (splashTimerFinished) {
                        Log.d(TAG, "✅ Timer already finished - proceeding immediately");
                        checkAdStatusAndProceed();
                    }
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError adError) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.w(TAG, "❌ SCALABLE FAILURE: Interstitial ad failed to load during background loading");
                    Log.w(TAG, "⏰ Failed after: " + loadTime + "ms");
                    Log.w(TAG, "📊 Error details: " + adError.getMessage());
                    Log.w(TAG, "🔢 Error code: " + adError.getCode());
                    Log.w(TAG, "🌐 Error domain: " + adError.getDomain());

                    mInterstitialAd = null;
                    adLoaded = false; // Ad failed to load

                    Log.d(TAG, "⏭️ SCALABLE FALLBACK: Will proceed to MainActivity after 3-second timer");

                    // Check if 3-second timer has finished
                    if (splashTimerFinished) {
                        Log.d(TAG, "✅ Timer already finished - proceeding to MainActivity without ad");
                        checkAdStatusAndProceed();
                    }
                }
            });
    }

    /**
     * ✅ Check and Proceed - Smart Logic for Splash + Ad Loading
     * Waits for BOTH minimum splash time AND ad loading to complete
     */
    private void checkAndProceed() {
        Log.d(TAG, "🔍 CHECKING STATUS:");
        Log.d(TAG, "   📱 Splash finished: " + splashFinished);
        Log.d(TAG, "   📺 Ad loaded: " + adLoaded);

        if (splashFinished && adLoaded) {
            // Both conditions met - proceed immediately
            Log.d(TAG, "✅ BOTH READY: Splash time complete AND ad loading finished");
            showInterstitialAdOrProceed();
        } else if (splashFinished && !adLoaded) {
            // Splash time is done but ad still loading - wait for ad
            Log.d(TAG, "⏳ WAITING: Splash time complete, but ad still loading...");
            Log.d(TAG, "🔄 Will show ad immediately when it loads");
        } else if (!splashFinished && adLoaded) {
            // Ad is ready but splash time not complete - wait for splash
            Log.d(TAG, "⏳ WAITING: Ad ready, but waiting for minimum splash time...");
        } else {
            // Neither ready - continue waiting
            Log.d(TAG, "⏳ WAITING: Both splash time and ad loading still in progress...");
        }
    }

    /**
     * ✅ Show Interstitial Ad or Proceed - Enhanced with Background Loading Status
     * Shows ad if background loading succeeded, otherwise goes directly to MainActivity
     */
    private void showInterstitialAdOrProceed() {
        Log.d(TAG, "🏁 READY TO PROCEED: Both splash time and ad loading are complete!");

        if (mInterstitialAd != null && !adShown) {
            Log.d(TAG, "🎯 SHOWING INTERSTITIAL AD: Ad loaded successfully during splash");
            Log.d(TAG, "⏳ Waiting for user to close ad before navigating to Home...");

            adShown = true; // Mark ad as shown to prevent multiple displays

            // Set up full screen content callback - ONLY navigate after user action
            mInterstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    Log.d(TAG, "✅ USER CLOSED AD: User dismissed interstitial ad");
                    Log.d(TAG, "🏠 NAVIGATING TO HOME: Going to MainActivity now");
                    mInterstitialAd = null;
                    navigateToMainActivity();
                }

                @Override
                public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                    Log.w(TAG, "❌ FAILED TO SHOW AD: " + adError.getMessage());
                    Log.d(TAG, "🏠 NAVIGATING TO HOME: Going to MainActivity without ad");
                    mInterstitialAd = null;
                    navigateToMainActivity();
                }

                @Override
                public void onAdShowedFullScreenContent() {
                    Log.d(TAG, "📺 AD DISPLAYED: Interstitial ad is now showing to user");
                    Log.d(TAG, "⏳ Waiting for user to close ad...");
                }

                @Override
                public void onAdClicked() {
                    Log.d(TAG, "👆 USER CLICKED AD: User interacted with the ad");
                    // Note: Don't navigate here - wait for dismiss
                }

                @Override
                public void onAdImpression() {
                    Log.d(TAG, "👁️ AD IMPRESSION: Ad impression recorded for revenue");
                }
            });

            // Show the ad that was loaded in background
            Log.d(TAG, "🚀 DISPLAYING AD: Showing interstitial ad to user now");
            mInterstitialAd.show(this);

        } else if (adShown) {
            Log.d(TAG, "⚠️ AD ALREADY SHOWN: Interstitial ad was already displayed");
            Log.d(TAG, "🏠 NAVIGATING TO HOME: Going to MainActivity");
            navigateToMainActivity();

        } else {
            Log.d(TAG, "⚠️ NO AD AVAILABLE: No interstitial ad to show (loading failed)");
            Log.d(TAG, "🏠 NAVIGATING TO HOME: Going directly to MainActivity");
            navigateToMainActivity();
        }
    }

    /**
     * ✅ Navigate to Main Activity (Home Screen)
     * Final navigation to MainActivity - only called after user closes ad
     */
    private void navigateToMainActivity() {
        Log.d(TAG, "🏠 FINAL NAVIGATION: Starting MainActivity (Home Screen)");
        Log.d(TAG, "✅ FLOW COMPLETE: Splash → Ad Loading → Ad Display → User Action → Home");

        Intent intent = new Intent(SplashActivity.this, MainActivity.class);
        startActivity(intent);

        // Close splash activity
        finish();

        // Add smooth transition animation
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

        Log.d(TAG, "🎉 SUCCESS: User is now in MainActivity (Home Screen)");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Clean up interstitial ad
        if (mInterstitialAd != null) {
            mInterstitialAd = null;
        }
    }

    @Override
    public void onBackPressed() {
        // Disable back button during splash screen and ad display
        Log.d(TAG, "🚫 BACK BUTTON DISABLED: User tried to go back during splash/ad");
        Log.d(TAG, "⏳ Please wait for the process to complete...");
        // Do nothing - prevent user from interrupting the flow
    }
}
