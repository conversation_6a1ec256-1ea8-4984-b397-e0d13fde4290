<?xml version="1.0" encoding="utf-8"?>
<!-- ✅ TEST LAYOUT - Preview How Native Ad Will Look -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#F5F5F5">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Native Ad Preview:"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <!-- This shows exactly how the native ad will appear -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:minHeight="120dp"
        android:layout_margin="8dp"
        android:background="#FFFFFF"
        android:elevation="2dp"
        android:padding="16dp">

        <!-- Top Row: Ad Attribution -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="#E0E0E0"
                android:padding="4dp"
                android:text="Ad"
                android:textColor="#666666"
                android:textSize="10sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Main Content Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="top">

            <!-- App Icon -->
            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp">

                <View
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:background="#E0E0E0" />

                <TextView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:text="LOGO"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:textColor="#666666" />

            </FrameLayout>

            <!-- Content Section -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="16dp"
                android:orientation="vertical">

                <!-- Headline -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp"
                    android:text="Amazing App Name Here" />

                <!-- Body Text -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp"
                    android:text="This is the app description that shows what the app does" />

                <!-- Advertiser -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#888888"
                    android:textSize="12sp"
                    android:layout_marginBottom="4dp"
                    android:text="Developer Name" />

                <!-- Star Rating -->
                <RatingBar
                    style="?android:attr/ratingBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:isIndicator="true"
                    android:numStars="5"
                    android:stepSize="0.5"
                    android:rating="4.5"
                    android:layout_marginBottom="4dp" />

                <!-- Price -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#1E88E5"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:text="Free" />

            </LinearLayout>

            <!-- Call to Action Button -->
            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <Button
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:backgroundTint="#1E88E5"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:text="Install"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:minWidth="80dp" />

            </FrameLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
