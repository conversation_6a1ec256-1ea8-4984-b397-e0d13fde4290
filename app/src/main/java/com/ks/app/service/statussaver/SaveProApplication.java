package com.ks.app.service.statussaver;

import android.app.Application;
import android.util.Log;
import com.ks.app.service.statussaver.utils.AdMobConfig;

/**
 * ✅ SavePro Application Class
 * Handles app-wide initialization including AdMob SDK
 */
public class SaveProApplication extends Application {

    private static final String TAG = "SaveProApplication";

    @Override
    public void onCreate() {
        super.onCreate();
        
        Log.d(TAG, "SavePro – Status Downloader application starting...");
        
        // ✅ Initialize AdMob SDK
        initializeAdMob();
        
        Log.d(TAG, "SavePro application initialization complete");
    }
    
    /**
     * ✅ Initialize AdMob SDK - Optimized for Background Loading
     * Early initialization to enable fast ad loading during splash screen
     */
    private void initializeAdMob() {
        try {
            Log.d(TAG, "🚀 Early AdMob SDK initialization for background loading...");

            // Initialize AdMob with production configuration early
            AdMobConfig.initialize(this);

            // Production mode - test mode disabled for live ads
            Log.d(TAG, "✅ AdMob initialized for production with App ID: ca-app-pub-7557152164205920~6417975169");
            Log.d(TAG, "⚡ SDK ready for background ad loading during splash screen");

            Log.d(TAG, "AdMob SDK initialization completed - ready for fast ad loading");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing AdMob SDK: " + e.getMessage(), e);
        }
    }
}
