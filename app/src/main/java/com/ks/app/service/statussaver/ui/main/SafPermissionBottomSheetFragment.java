package com.ks.app.service.statussaver.ui.main;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.animation.ObjectAnimator;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.ks.app.service.statussaver.R;

public class SafPermissionBottomSheetFragment extends BottomSheetDialogFragment {

    public static final String TAG = "AccessPermissionBottomSheetFragment";

    private AccessPermissionListener listener;
    private TextView fingerClickAnimation;
    private Handler animationHandler;
    private Runnable fingerClickRunnable;

    public interface AccessPermissionListener {
        void onAllowAccess();
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof AccessPermissionListener) {
            listener = (AccessPermissionListener) context;
        } else {
            throw new ClassCastException(context.toString() + " must implement AccessPermissionListener");
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // Set custom dark theme for full screen permission dialog
        setStyle(STYLE_NORMAL, R.style.Theme_PermissionBottomSheetDialog);
        animationHandler = new Handler(Looper.getMainLooper());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.bottom_sheet_saf_permission, container, false);

        // Initialize views
        Button allowButton = view.findViewById(R.id.button_allow_access);
        fingerClickAnimation = view.findViewById(R.id.finger_click_animation);

        // Setup button click listener
        allowButton.setOnClickListener(v -> {
            if (listener != null) {
                listener.onAllowAccess();
            }
            dismiss();
        });

        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Make bottom sheet full screen
        setupFullScreenBottomSheet();

        // Start finger click animation
        startFingerClickAnimation();
    }

    /**
     * ✅ Setup Full Screen Bottom Sheet
     */
    private void setupFullScreenBottomSheet() {
        if (getDialog() instanceof BottomSheetDialog) {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) getDialog();
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);

            if (bottomSheet != null) {
                BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(bottomSheet);
                behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                behavior.setSkipCollapsed(true);
                behavior.setHideable(true);

                // Set full height
                ViewGroup.LayoutParams layoutParams = bottomSheet.getLayoutParams();
                layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT;
                bottomSheet.setLayoutParams(layoutParams);
            }
        }
    }

    /**
     * ✅ Start Finger Click Animation on USE THIS FOLDER button
     */
    private void startFingerClickAnimation() {
        if (fingerClickAnimation != null) {
            // Show finger click animation after 2 seconds
            fingerClickRunnable = () -> {
                fingerClickAnimation.setVisibility(View.VISIBLE);

                // Create simple click animation (no pulsing)
                ObjectAnimator fadeOut = ObjectAnimator.ofFloat(fingerClickAnimation, "alpha", 0.8f, 0.3f);
                ObjectAnimator fadeIn = ObjectAnimator.ofFloat(fingerClickAnimation, "alpha", 0.3f, 0.8f);

                // Set repeat count
                fadeOut.setRepeatCount(ValueAnimator.INFINITE);
                fadeIn.setRepeatCount(ValueAnimator.INFINITE);

                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playSequentially(fadeOut, fadeIn);
                animatorSet.setDuration(800); // Simple fade animation
                animatorSet.start();
            };

            animationHandler.postDelayed(fingerClickRunnable, 2000);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // Clean up animation handler
        if (animationHandler != null && fingerClickRunnable != null) {
            animationHandler.removeCallbacks(fingerClickRunnable);
        }
    }

}