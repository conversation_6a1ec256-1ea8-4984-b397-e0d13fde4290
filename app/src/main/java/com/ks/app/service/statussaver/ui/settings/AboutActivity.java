package com.ks.app.service.statussaver.ui.settings;

import android.os.Bundle;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.ks.app.service.statussaver.R;

/**
 * About Activity - Clean version without email functionality
 */
public class AboutActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        setTheme(R.style.Theme_SettingsStatusBar);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_about);

        setupBackButton();
        setupContent();
    }

    private void setupBackButton() {
        ImageView backIcon = findViewById(R.id.back_icon);
        backIcon.setImageResource(R.drawable.ic_arrow_back_24);
        backIcon.setOnClickListener(v -> finish());
    }

    private void setupContent() {
        TextView contentText = findViewById(R.id.content_text);
        TextView emailText = findViewById(R.id.email_text);

        String aboutContent = "About SavePro – Status Downloader\n\n" +
                "SavePro – Status Downloader is a lightweight and user-friendly mobile application designed to help you quickly download and save status videos and images from popular social platforms directly to your device. Prioritizing user privacy, SavePro operates with minimal permissions and does not collect any personal data.\n\n" +
                "Our app delivers a secure and reliable experience, allowing you to save your favorite status updates effortlessly while keeping your media safe and accessible only on your device.";

        contentText.setText(aboutContent);

        // Remove email section completely - no email display or functionality needed
        emailText.setVisibility(android.view.View.GONE);
    }
}
