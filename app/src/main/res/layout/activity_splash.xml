<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="32dp">

    <!-- Download Icon -->
    <ImageView
        android:id="@+id/splash_icon"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="24dp"
        android:src="@drawable/download_icon"
        android:scaleType="fitCenter"
        android:contentDescription="@string/app_name" />

    <!-- App Name -->
    <TextView
        android:id="@+id/splash_app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:textColor="@android:color/white"
        android:textSize="24sp"
        android:textStyle="bold"
        android:fontFamily="sans-serif-medium"
        android:gravity="center" />

    <!-- Loading Indicator -->
    <ProgressBar
        android:id="@+id/splash_loading_indicator"
        style="?android:attr/progressBarStyle"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="32dp"
        android:indeterminateTint="@android:color/white"
        android:indeterminateTintMode="src_in" />

</LinearLayout>
