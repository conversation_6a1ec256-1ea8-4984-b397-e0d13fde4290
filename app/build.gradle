plugins {
    id 'com.android.application'
}

android {
    namespace "com.ks.app.service.statussaver"
    compileSdk 34

    defaultConfig {
        applicationId "com.ks.app.service.statussaver"
        minSdk 21
        targetSdk 34
        versionCode 5
        versionName "1.4"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // Support vector drawables on older devices
        vectorDrawables.useSupportLibrary = true

        // Set to true ONLY if your method count exceeds 65,536
        multiDexEnabled false
    }

    signingConfigs {
        release {
            storeFile file(RELEASE_STORE_FILE)  // Your keystore path
            storePassword RELEASE_STORE_PASSWORD
            keyAlias RELEASE_KEY_ALIAS
            keyPassword RELEASE_KEY_PASSWORD
        }
    }

    buildTypes {
        debug {
            applicationIdSuffix ".debug"
            debuggable true
            minifyEnabled false
            shrinkResources false
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            debuggable false

            // Proguard rules for optimization and obfuscation
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            zipAlignEnabled true
        }
    }

    // Split APKs per ABI to reduce APK size
    splits {
        abi {
            enable true
            reset()
            include 'armeabi-v7a', 'arm64-v8a' // Only needed ABIs for modern devices
            universalApk false
        }
    }

    // Exclude unnecessary files to reduce APK size
    packagingOptions {
        resources {
            excludes += [
                'META-INF/DEPENDENCIES',
                'META-INF/LICENSE',
                'META-INF/LICENSE.txt',
                'META-INF/NOTICE',
                'META-INF/NOTICE.txt',
                'META-INF/ASL2.0'
            ]
        }
    }

    // Lint options for faster builds and to avoid failing on lint errors
    lint {
        checkReleaseBuilds false
        abortOnError false
        disable 'InvalidPackage'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding false  // Enable if you want to use view binding
        dataBinding false  // Enable if you want to use data binding
        buildConfig true
        resValues true
    }
}

dependencies {
    // AndroidX core libraries
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.core:core:1.12.0'

    // UI Components
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // ExoPlayer for optimized video playback and memory management
    implementation 'com.google.android.exoplayer:exoplayer:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-core:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.19.1'

    // Image loading and zooming
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

    // UI Effects
    implementation 'com.facebook.shimmer:shimmer:0.5.0'

    // Google Mobile Ads SDK (AdMob) - Updated to latest version
    implementation 'com.google.android.gms:play-services-ads:22.6.0'

    // Paging for large data sets
    implementation 'androidx.paging:paging-runtime:3.2.1'

    // Lifecycle components for MVVM
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-runtime:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.7.0'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    // Debug only tools
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.12'
}
