package com.ks.app.service.statussaver.ui.main;

import android.os.Build;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.utils.PermissionUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.content.pm.PackageManager;
import android.content.Intent;
import android.net.Uri;
import android.provider.Settings;
import androidx.lifecycle.ViewModelProvider;
import com.ks.app.service.statussaver.utils.SAFUtils;

import com.ks.app.service.statussaver.utils.MediaStoreUtils;
import com.ks.app.service.statussaver.utils.NativeAdHelper;
import com.ks.app.service.statussaver.ui.main.MainViewModel;
import com.ks.app.service.statussaver.ui.main.RuntimePermissionBottomSheetFragment;
import com.ks.app.service.statussaver.ui.main.SafPermissionBottomSheetFragment;

import android.widget.Toast;
import android.util.Log;

import androidx.appcompat.widget.Toolbar;
import android.view.Menu;
import android.view.MenuItem;
import com.google.android.gms.ads.MobileAds;
import android.widget.FrameLayout;

public class MainActivity extends AppCompatActivity
    implements SafPermissionBottomSheetFragment.AccessPermissionListener,
               RuntimePermissionBottomSheetFragment.RuntimePermissionListener {

    private static final String TAG = "MainActivity";
    private MainViewModel mainViewModel;
    private ViewPager2 viewPager;
    private boolean userWentToSettings = false;

    // ✅ Flag to prevent Images/Videos tabs from refreshing during media permission changes
    private boolean isHandlingMediaPermissionChange = false;

    // ✅ Native ad container for bottom of home screen
    private FrameLayout nativeAdContainer;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        Log.d(TAG, "🚀 MainActivity onCreate - App started successfully after splash");

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        mainViewModel = new ViewModelProvider(this).get(MainViewModel.class);
        viewPager = findViewById(R.id.view_pager);

        // Initialize native ad
        setupNativeAd();

        handlePermissionsAndInitialLoad();
        
        mainViewModel.getIsSafAccessPromptNeeded().observe(this, needsPrompt -> {
            if (needsPrompt != null && needsPrompt && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                Log.d(TAG, "isSafAccessPromptNeeded LiveData triggered. Re-checking access.");
                checkAndShowPermissionPrompts(); 
                mainViewModel.resetSafAccessPromptNeeded();
            }
        });
    }

    private void handlePermissionsAndInitialLoad() {
        try {
            Log.d(TAG, "handlePermissionsAndInitialLoad called");

            // ✅ IMMEDIATE DATA LOADING - Start loading data right away without waiting for permissions
            Log.d(TAG, "Starting immediate data load without waiting for permissions");
            setupViewPagerAndTabs();
            if (mainViewModel != null) {
                // ✅ Images/Videos tabs load immediately (use folder access, not media permissions)
                Log.d(TAG, "Loading Images/Videos tabs immediately (folder access)");
                mainViewModel.loadWhatsAppStatuses();

                // ✅ Saved tab loads based on media permissions (may be empty if no permissions)
                Log.d(TAG, "Loading Saved tab (depends on media permissions)");
                mainViewModel.loadSavedMedia();
            }

            // Handle permissions in parallel (non-blocking)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                boolean hasValidSafAccess = false;
                try {
                    // ✅ Use clean version-specific access check with null safety
                    if (this != null && !isFinishing() && !isDestroyed()) {
                        hasValidSafAccess = SAFUtils.hasWhatsAppStatusAccess(this);
                    } else {
                        Log.w(TAG, "Activity is null, finishing, or destroyed - skipping SAF access check");
                        hasValidSafAccess = false;
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error checking SAF access: " + e.getMessage(), e);
                    hasValidSafAccess = false;
                } catch (Throwable t) {
                    Log.e(TAG, "Unexpected error checking SAF access: " + t.getMessage(), t);
                    hasValidSafAccess = false;
                }

                if (!hasValidSafAccess) {
                    Log.d(TAG, "SAF access not granted. Will check folder existence and prompt if needed.");
                    checkAndShowPermissionPrompts();
                } else {
                    Log.d(TAG, "SAF access already granted and valid.");
                }
            } else {
                Log.d(TAG, "Pre-Android Q. Checking runtime permissions in background.");
                ensureRuntimePermissionsInBackground();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in handlePermissionsAndInitialLoad: " + e.getMessage(), e);
            // Even if permissions fail, ensure UI is set up and data loads
            try {
                setupViewPagerAndTabs();
                if (mainViewModel != null) {
                    mainViewModel.loadWhatsAppStatuses();
                    mainViewModel.loadSavedMedia();
                }
            } catch (Exception fallbackError) {
                Log.e(TAG, "Fallback UI setup also failed: " + fallbackError.getMessage(), fallbackError);
            }
        }
    }

    private void ensureRuntimePermissionsAndLoad() {
        try {
            Log.d(TAG, "ensureRuntimePermissionsAndLoad called");

            boolean hasPermissions = false;
            try {
                // ✅ Null safety check before permission validation
                if (this != null && !isFinishing() && !isDestroyed()) {
                    hasPermissions = PermissionUtils.hasPermissions(this);
                } else {
                    Log.w(TAG, "Activity is null, finishing, or destroyed - skipping permission check");
                    hasPermissions = false;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error checking permissions: " + e.getMessage(), e);
                hasPermissions = false;
            } catch (Throwable t) {
                Log.e(TAG, "Unexpected error checking permissions: " + t.getMessage(), t);
                hasPermissions = false;
            }

            if (hasPermissions) {
                Log.d(TAG, "Runtime permissions already granted. Setting up UI and loading media.");
                try {
                    setupViewPagerAndTabs();
                    if (mainViewModel != null) {
                        // ✅ Images/Videos already loaded in initial load - don't reload them
                        // Only ensure Saved tab is loaded with permissions
                        Log.d(TAG, "Permissions available - ensuring Saved tab is loaded (Images/Videos already loaded)");
                        mainViewModel.loadSavedMedia();
                    } else {
                        Log.w(TAG, "mainViewModel is null, cannot load media");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error setting up UI or loading media: " + e.getMessage(), e);
                }
            } else {
                Log.d(TAG, "Runtime permissions not granted. Requesting them.");
                try {
                    // ✅ Null safety for permission request operations
                    if (this != null && !isFinishing() && !isDestroyed()) {
                        boolean isRequestInProgress = PermissionUtils.isPermissionRequestInProgress(this);
                        if (!isRequestInProgress) {
                            try {
                                // ✅ Null safety for fragment manager operations
                                if (getSupportFragmentManager() != null) {
                                    boolean hasRuntimeDialog = getSupportFragmentManager().findFragmentByTag(RuntimePermissionBottomSheetFragment.TAG) != null;
                                    boolean hasSafDialog = getSupportFragmentManager().findFragmentByTag(SafPermissionBottomSheetFragment.TAG) != null;

                                    if (!hasRuntimeDialog && !hasSafDialog) {
                                        PermissionUtils.requestPermissions(this);
                                    } else {
                                        Log.d(TAG, "A permission bottom sheet is already visible. Will not request runtime perms now.");
                                    }
                                } else {
                                    Log.w(TAG, "Fragment manager is null - cannot check for existing dialogs");
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error checking fragment manager or requesting permissions: " + e.getMessage(), e);
                            }
                        } else {
                            Log.d(TAG, "Runtime permission request already in progress.");
                        }
                    } else {
                        Log.w(TAG, "Activity is null, finishing, or destroyed - skipping permission request");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error checking permission request progress: " + e.getMessage(), e);
                } catch (Throwable t) {
                    Log.e(TAG, "Unexpected error in permission request: " + t.getMessage(), t);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in ensureRuntimePermissionsAndLoad: " + e.getMessage(), e);
        }
    }

    private void ensureRuntimePermissionsInBackground() {
        try {
            Log.d(TAG, "ensureRuntimePermissionsInBackground called");

            boolean hasPermissions = false;
            try {
                // ✅ Null safety check before permission validation
                if (this != null && !isFinishing() && !isDestroyed()) {
                    hasPermissions = PermissionUtils.hasPermissions(this);
                } else {
                    Log.w(TAG, "Activity is null, finishing, or destroyed - skipping permission check");
                    return;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error checking permissions: " + e.getMessage(), e);
                return;
            }

            if (!hasPermissions) {
                Log.d(TAG, "Missing permissions. Requesting them in background.");
                try {
                    // ✅ Null safety for permission request
                    if (this != null && !isFinishing() && !isDestroyed()) {
                        PermissionUtils.requestPermissions(this);
                    } else {
                        Log.w(TAG, "Activity is null, finishing, or destroyed - cannot request permissions");
                    }
                } catch (Throwable t) {
                    Log.e(TAG, "Unexpected error in permission request: " + t.getMessage(), t);
                }
            } else {
                Log.d(TAG, "All permissions already granted.");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in ensureRuntimePermissionsInBackground: " + e.getMessage(), e);
        }
    }

    private void checkAndShowPermissionPrompts() {
        try {
            Log.d(TAG, "checkAndShowPermissionPrompts called.");

            try {
                boolean hasSafDialog = getSupportFragmentManager().findFragmentByTag(SafPermissionBottomSheetFragment.TAG) != null;
                boolean hasRuntimeDialog = getSupportFragmentManager().findFragmentByTag(RuntimePermissionBottomSheetFragment.TAG) != null;

                if (hasSafDialog || hasRuntimeDialog) {
                    Log.d(TAG, "A permission-related bottom sheet is already visible. Returning.");
                    return;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error checking existing dialogs: " + e.getMessage(), e);
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                boolean hasValidSafAccess = false;
                try {
                    // ✅ Use clean version-specific access check with null safety
                    if (this != null && !isFinishing() && !isDestroyed()) {
                        hasValidSafAccess = SAFUtils.hasWhatsAppStatusAccess(this);
                    } else {
                        Log.w(TAG, "Activity is null, finishing, or destroyed - skipping SAF access check in prompts");
                        hasValidSafAccess = false;
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error checking SAF access in prompts: " + e.getMessage(), e);
                    hasValidSafAccess = false;
                } catch (Throwable t) {
                    Log.e(TAG, "Unexpected error checking SAF access in prompts: " + t.getMessage(), t);
                    hasValidSafAccess = false;
                }

                if (!hasValidSafAccess) {
                    try {
                        // For Android 11+, always show SAF prompt if no access
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                            Log.i(TAG, "Android Q+: WhatsApp .Statuses folder exists, but no valid SAF access. Showing SAF prompt.");
                            try {
                                SafPermissionBottomSheetFragment bottomSheet = new SafPermissionBottomSheetFragment();
                                bottomSheet.setCancelable(false);
                                bottomSheet.show(getSupportFragmentManager(), SafPermissionBottomSheetFragment.TAG);
                                return;
                            } catch (Exception e) {
                                Log.e(TAG, "Error showing SAF bottom sheet: " + e.getMessage(), e);
                            }
                        } else {
                            Log.i(TAG, "Android Q+: WhatsApp .Statuses folder NOT found. Not showing SAF prompt. Will proceed to runtime permission check if necessary.");
                            try {
                                // ✅ Null safety for UI setup and permission check
                                boolean hasPerms = false;
                                try {
                                    if (this != null && !isFinishing() && !isDestroyed()) {
                                        hasPerms = PermissionUtils.hasPermissions(this);
                                    }
                                } catch (Exception permE) {
                                    Log.e(TAG, "Error checking permissions for UI setup: " + permE.getMessage(), permE);
                                    hasPerms = false;
                                }

                                if (viewPager != null && viewPager.getAdapter() == null && hasPerms) {
                                    setupViewPagerAndTabs();
                                }
                                if (mainViewModel != null) {
                                    mainViewModel.loadWhatsAppStatuses();
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error setting up UI or loading statuses: " + e.getMessage(), e);
                            }
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking folder existence: " + e.getMessage(), e);
                    }
                } else {
                     Log.i(TAG, "Android Q+: Has valid SAF access already.");
                     ensureRuntimePermissionsAndLoad();
                     return;
                }
            }

            Log.d(TAG, "Proceeding to check/request runtime permissions if needed.");
            ensureRuntimePermissionsAndLoad();
        } catch (Exception e) {
            Log.e(TAG, "Error in checkAndShowPermissionPrompts: " + e.getMessage(), e);
            // Fallback to basic permission check
            try {
                ensureRuntimePermissionsAndLoad();
            } catch (Exception fallbackError) {
                Log.e(TAG, "Fallback permission check also failed: " + fallbackError.getMessage(), fallbackError);
            }
        }
    }

    private void setupViewPagerAndTabs() {
        try {
            if (viewPager == null) {
                Log.e(TAG, "setupViewPagerAndTabs: viewPager is null");
                return;
            }

            Log.d(TAG, "setupViewPagerAndTabs: Setting up adapter and tabs.");
            TabLayout tabLayout = findViewById(R.id.tab_layout);

            if (tabLayout == null) {
                Log.e(TAG, "setupViewPagerAndTabs: tabLayout is null");
                return;
            }

            try {
                // Use the main pager adapter (fallback to existing working fragments)
                MainPagerAdapter adapter = new MainPagerAdapter(this);
                viewPager.setAdapter(adapter);

                // Configure ViewPager2 for better performance
                viewPager.setOffscreenPageLimit(3); // Keep all tabs in memory
                viewPager.setUserInputEnabled(true); // Allow swiping

                new TabLayoutMediator(tabLayout, viewPager,
                        (tab, position) -> {
                            try {
                                // Show tabs with icons and text
                                switch (position) {
                                    case 0:
                                        tab.setText(getString(R.string.images_tab_title));
                                        tab.setIcon(R.drawable.ic_tab_images);
                                        break;
                                    case 1:
                                        tab.setText(getString(R.string.videos_tab_title));
                                        tab.setIcon(R.drawable.ic_tab_videos);
                                        break;
                                    case 2:
                                        tab.setText(getString(R.string.saved_tab_title));
                                        tab.setIcon(R.drawable.ic_tab_saved);
                                        break;
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error setting tab text and icon: " + e.getMessage(), e);
                                // Fallback tab names with icons
                                switch (position) {
                                    case 0:
                                        tab.setText("Images");
                                        tab.setIcon(R.drawable.ic_tab_images);
                                        break;
                                    case 1:
                                        tab.setText("Videos");
                                        tab.setIcon(R.drawable.ic_tab_videos);
                                        break;
                                    case 2:
                                        tab.setText("Saved");
                                        tab.setIcon(R.drawable.ic_tab_saved);
                                        break;
                                }
                            }
                        }
                ).attach();

                Log.d(TAG, "ViewPager and tabs setup complete");
            } catch (Exception e) {
                Log.e(TAG, "Error setting up ViewPager adapter: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in setupViewPagerAndTabs: " + e.getMessage(), e);
        }
    }

    /**
     * ✅ Refresh only Saved tab data without affecting Images/Videos tabs
     * This prevents unnecessary reloading of Images/Videos when permissions change
     */
    private void refreshSavedTabOnly() {
        try {
            Log.d(TAG, "refreshSavedTabOnly: Refreshing only Saved tab data");

            if (mainViewModel != null) {
                Log.d(TAG, "Reloading Saved tab data only");
                mainViewModel.loadSavedMedia();
            } else {
                Log.w(TAG, "mainViewModel is null, cannot refresh Saved tab");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error refreshing Saved tab: " + e.getMessage(), e);
        }
    }

    private void refreshTabsForPermissionChange() {
        try {
            Log.d(TAG, "refreshTabsForPermissionChange: Refreshing tabs due to permission change");

            if (viewPager == null) {
                Log.e(TAG, "refreshTabsForPermissionChange: viewPager is null");
                return;
            }

            TabLayout tabLayout = findViewById(R.id.tab_layout);
            if (tabLayout == null) {
                Log.e(TAG, "refreshTabsForPermissionChange: tabLayout is null");
                return;
            }

            // Clear existing adapter
            viewPager.setAdapter(null);

            // Create new adapter with current permission state
            MainPagerAdapter adapter = new MainPagerAdapter(this);
            viewPager.setAdapter(adapter);

            // Configure ViewPager2 for better performance
            viewPager.setOffscreenPageLimit(3);
            viewPager.setUserInputEnabled(true);

            // Setup tabs with new adapter
            new TabLayoutMediator(tabLayout, viewPager,
                    (tab, position) -> {
                        try {
                            // Show tabs with icons and text
                            switch (position) {
                                case 0:
                                    tab.setText(getString(R.string.images_tab_title));
                                    tab.setIcon(R.drawable.ic_tab_images);
                                    break;
                                case 1:
                                    tab.setText(getString(R.string.videos_tab_title));
                                    tab.setIcon(R.drawable.ic_tab_videos);
                                    break;
                                case 2:
                                    tab.setText(getString(R.string.saved_tab_title));
                                    tab.setIcon(R.drawable.ic_tab_saved);
                                    break;
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error setting tab text and icon in refresh: " + e.getMessage(), e);
                            // Fallback tab names with icons
                            switch (position) {
                                case 0:
                                    tab.setText("Images");
                                    tab.setIcon(R.drawable.ic_tab_images);
                                    break;
                                case 1:
                                    tab.setText("Videos");
                                    tab.setIcon(R.drawable.ic_tab_videos);
                                    break;
                                case 2:
                                    tab.setText("Saved");
                                    tab.setIcon(R.drawable.ic_tab_saved);
                                    break;
                            }
                        }
                    }
            ).attach();

            Log.d(TAG, "Tabs refreshed successfully. Always showing 3 tabs.");

        } catch (Exception e) {
            Log.e(TAG, "Error in refreshTabsForPermissionChange: " + e.getMessage(), e);
        }
    }

    @Override
    public void onAllowAccess() {
        try {
            Log.d(TAG, "onAllowAccess called from bottom sheet (user clicked Allow).");

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // ✅ Null safety for SAF request
                try {
                    if (this != null && !isFinishing() && !isDestroyed()) {
                        SAFUtils.requestSAFAccess(this);
                    } else {
                        Log.w(TAG, "Activity is null, finishing, or destroyed - cannot request SAF access");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error requesting SAF access: " + e.getMessage(), e);
                }
            } else {
                // Android 7-10: File access is automatic with permissions
                ensureRuntimePermissionsAndLoad();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in onAllowAccess: " + e.getMessage(), e);
        }
    }

    @Override
    public void onGoToSettings() {
        try {
            // Set flag to track that user went to settings
            userWentToSettings = true;
            Log.d(TAG, "User going to settings - setting flag");

            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            String packageName = getPackageName();
            if (packageName != null) {
                Uri uriValue = Uri.fromParts("package", packageName, null);
                intent.setData(uriValue);
                startActivity(intent);
            } else {
                Log.e(TAG, "Package name is null, cannot open settings");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error opening settings: " + e.getMessage(), e);
        }
    }

    @Override
    public void onCancelRuntimePermission() {
        try {
            // Runtime permissions denied - app may not function correctly
        } catch (Exception e) {
            Log.e(TAG, "Error in onCancelRuntimePermission: " + e.getMessage(), e);
        }
    }

    /**
     * Public method to request runtime permissions - can be called from fragments
     * ✅ Modified to NOT reload Images/Videos tabs when called from fragments
     */
    public void requestRuntimePermissions() {
        try {
            Log.d(TAG, "requestRuntimePermissions called from fragment - requesting permissions only (no tab refresh)");

            // ✅ Only request permissions, don't reload Images/Videos tabs
            if (this != null && !isFinishing() && !isDestroyed()) {
                boolean hasPermissions = PermissionUtils.hasPermissions(this);
                if (!hasPermissions) {
                    boolean isRequestInProgress = PermissionUtils.isPermissionRequestInProgress(this);
                    if (!isRequestInProgress) {
                        PermissionUtils.requestPermissions(this);
                    }
                } else {
                    Log.d(TAG, "Permissions already granted - only refreshing Saved tab");
                    if (mainViewModel != null) {
                        isHandlingMediaPermissionChange = true;
                        mainViewModel.setSkipImagesVideosRefresh(true);
                        mainViewModel.loadSavedMediaOnly();
                        mainViewModel.setSkipImagesVideosRefresh(false);
                        isHandlingMediaPermissionChange = false;
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in requestRuntimePermissions: " + e.getMessage(), e);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        try {
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);

            // ✅ Null safety for permission result handling
            try {
                if (this != null && !isFinishing() && !isDestroyed()) {
                    PermissionUtils.onRequestPermissionsResultReceived();
                } else {
                    Log.w(TAG, "Activity is null, finishing, or destroyed - skipping permission result handling");
                    return;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error in onRequestPermissionsResultReceived: " + e.getMessage(), e);
            }

            if (requestCode == PermissionUtils.PERMISSIONS_REQUEST_CODE) {
                boolean hasPermissions = false;
                try {
                    // ✅ Null safety for permission check in result
                    if (this != null && !isFinishing() && !isDestroyed()) {
                        hasPermissions = PermissionUtils.hasPermissions(this);
                    } else {
                        Log.w(TAG, "Activity is null, finishing, or destroyed - skipping permission check in result");
                        hasPermissions = false;
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error checking permissions in result: " + e.getMessage(), e);
                    hasPermissions = false;
                } catch (Throwable t) {
                    Log.e(TAG, "Unexpected error checking permissions in result: " + t.getMessage(), t);
                    hasPermissions = false;
                }

                if (hasPermissions) {
                    Log.d(TAG, "Runtime permissions GRANTED after request.");
                    try {
                        if (viewPager != null && viewPager.getAdapter() == null) {
                            setupViewPagerAndTabs();
                        }

                        // ✅ Use new method to load ONLY Saved tab
                        if (mainViewModel != null) {
                            Log.d(TAG, "Media permissions granted - loading ONLY Saved tab (Images/Videos unchanged)");
                            isHandlingMediaPermissionChange = true;
                            mainViewModel.setSkipImagesVideosRefresh(true);
                            mainViewModel.loadSavedMediaOnly();
                            mainViewModel.setSkipImagesVideosRefresh(false);
                            isHandlingMediaPermissionChange = false;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error setting up UI after permission grant: " + e.getMessage(), e);
                    }
                } else {
                    Log.d(TAG, "Runtime permissions DENIED after request.");
                    boolean shouldShowSettingsLink = false;

                    try {
                        if (permissions != null) {
                            for(String permission : permissions) {
                                if (permission != null) {
                                    boolean shouldShow = ActivityCompat.shouldShowRequestPermissionRationale(this, permission);
                                    int permissionStatus = ContextCompat.checkSelfPermission(this, permission);

                                    if (!shouldShow && permissionStatus != PackageManager.PERMISSION_GRANTED) {
                                        shouldShowSettingsLink = true;
                                        break;
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking permission rationale: " + e.getMessage(), e);
                        shouldShowSettingsLink = true; // Default to showing settings link on error
                    }

                    if (shouldShowSettingsLink) {
                        Log.i(TAG, "Runtime permission denied with Don't Ask Again. Showing settings bottom sheet.");
                        try {
                            showRuntimePermissionDeniedBottomSheet();
                        } catch (Exception e) {
                            Log.e(TAG, "Error showing permission denied bottom sheet: " + e.getMessage(), e);
                        }
                    } else {
                        Log.i(TAG, "Runtime permission denied, can ask again or partially granted. User will be prompted later or can refresh.");
                        try {
                            if (viewPager != null && viewPager.getAdapter() == null) {
                                setupViewPagerAndTabs();
                            }

                            // ✅ Ensure Images/Videos tabs are protected from any refresh attempts
                            if (mainViewModel != null) {
                                Log.d(TAG, "Media permissions denied - ensuring NO tab refresh (Images/Videos unchanged, Saved shows empty state)");
                                isHandlingMediaPermissionChange = true;
                                mainViewModel.setSkipImagesVideosRefresh(true);
                                // Don't call loadSavedMedia() - let it show empty state naturally
                                mainViewModel.setSkipImagesVideosRefresh(false);
                                isHandlingMediaPermissionChange = false;
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error handling partial permission grant: " + e.getMessage(), e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in onRequestPermissionsResult: " + e.getMessage(), e);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        try {
            super.onActivityResult(requestCode, resultCode, data);

            if (requestCode == SAFUtils.REQUEST_CODE_SAF_WHATSAPP_STATUSES) {
                if (resultCode == RESULT_OK && data != null) {
                    // ✅ Null safety for SAF result handling
                    try {
                        if (this != null && !isFinishing() && !isDestroyed()) {
                            // SAF permission granted - persist the URI
                            String treeUriKey = SAFUtils.getWhatsAppStatusesTreeUriKey();
                            if (treeUriKey != null) {
                                SAFUtils.persistFolderUri(this, data, treeUriKey);
                            } else {
                                Log.w(TAG, "Tree URI key is null - cannot persist SAF URI");
                            }

                            ensureRuntimePermissionsAndLoad();
                        } else {
                            Log.w(TAG, "Activity is null, finishing, or destroyed - skipping SAF result handling");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error handling SAF success result: " + e.getMessage(), e);
                    }
                } else {
                    // SAF permission denied
                    try {

                        // Show SAF dialog again for Android 11+
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                            try {
                                boolean hasSafDialog = getSupportFragmentManager().findFragmentByTag(SafPermissionBottomSheetFragment.TAG) != null;
                                boolean hasRuntimeDialog = getSupportFragmentManager().findFragmentByTag(RuntimePermissionBottomSheetFragment.TAG) != null;

                                if (!hasSafDialog && !hasRuntimeDialog) {
                                    SafPermissionBottomSheetFragment bottomSheet = new SafPermissionBottomSheetFragment();
                                    bottomSheet.setCancelable(false);
                                    bottomSheet.show(getSupportFragmentManager(), SafPermissionBottomSheetFragment.TAG);
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error showing SAF dialog after cancellation: " + e.getMessage(), e);
                            }
                        } else {
                            Log.i(TAG, "WhatsApp .Statuses folder not found. Not re-prompting SAF.");
                            ensureRuntimePermissionsAndLoad();
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error handling SAF cancellation: " + e.getMessage(), e);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in onActivityResult: " + e.getMessage(), e);
        }
    }

    private void showRuntimePermissionDeniedBottomSheet() {
        try {
            // ✅ Null safety for showing permission denied bottom sheet
            if (this == null || isFinishing() || isDestroyed()) {
                Log.w(TAG, "Activity is null, finishing, or destroyed - cannot show permission denied bottom sheet");
                return;
            }

            boolean hasRuntimeDialog = false;
            boolean hasSafDialog = false;

            try {
                if (getSupportFragmentManager() != null) {
                    hasRuntimeDialog = getSupportFragmentManager().findFragmentByTag(RuntimePermissionBottomSheetFragment.TAG) != null;
                    hasSafDialog = getSupportFragmentManager().findFragmentByTag(SafPermissionBottomSheetFragment.TAG) != null;
                } else {
                    Log.w(TAG, "Fragment manager is null - cannot check existing dialogs");
                    return;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error checking existing dialogs in showRuntimePermissionDeniedBottomSheet: " + e.getMessage(), e);
                return;
            }

            if (!hasRuntimeDialog && !hasSafDialog) {
                try {
                    RuntimePermissionBottomSheetFragment bottomSheet = new RuntimePermissionBottomSheetFragment();
                    if (bottomSheet != null) {
                        bottomSheet.setCancelable(false);
                        bottomSheet.show(getSupportFragmentManager(), RuntimePermissionBottomSheetFragment.TAG);
                    } else {
                        Log.e(TAG, "Failed to create RuntimePermissionBottomSheetFragment");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error showing runtime permission denied bottom sheet: " + e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in showRuntimePermissionDeniedBottomSheet: " + e.getMessage(), e);
        } catch (Throwable t) {
            Log.e(TAG, "Unexpected error in showRuntimePermissionDeniedBottomSheet: " + t.getMessage(), t);
        }
    }

    @Override
    protected void onResume() {
        try {
            super.onResume();
            Log.d(TAG, "onResume: Re-evaluating permissions and loading state.");

            try {
                // ✅ Check if permissions are now granted with null safety
                boolean hasRuntimePermissions = false;
                boolean hasSafPermissions = true;

                // Check runtime permissions with null safety
                try {
                    if (this != null && !isFinishing() && !isDestroyed()) {
                        hasRuntimePermissions = PermissionUtils.hasPermissions(this);
                    } else {
                        Log.w(TAG, "Activity is null, finishing, or destroyed - skipping runtime permission check in onResume");
                        hasRuntimePermissions = false;
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error checking runtime permissions in onResume: " + e.getMessage(), e);
                    hasRuntimePermissions = false;
                } catch (Throwable t) {
                    Log.e(TAG, "Unexpected error checking runtime permissions in onResume: " + t.getMessage(), t);
                    hasRuntimePermissions = false;
                }

                // Check SAF permissions with null safety
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    try {
                        if (this != null && !isFinishing() && !isDestroyed()) {
                            hasSafPermissions = SAFUtils.hasWhatsAppStatusAccess(this);
                        } else {
                            Log.w(TAG, "Activity is null, finishing, or destroyed - skipping SAF permission check in onResume");
                            hasSafPermissions = true; // Don't block if activity is invalid
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking SAF permissions in onResume: " + e.getMessage(), e);
                        hasSafPermissions = true; // Don't block if SAF check fails
                    } catch (Throwable t) {
                        Log.e(TAG, "Unexpected error checking SAF permissions in onResume: " + t.getMessage(), t);
                        hasSafPermissions = true; // Don't block if SAF check fails
                    }
                }

                Log.d(TAG, "onResume - Runtime permissions: " + hasRuntimePermissions + ", SAF permissions: " + hasSafPermissions + ", userWentToSettings: " + userWentToSettings);

                // If permissions are now granted, dismiss any permission dialogs and setup UI
                if (hasRuntimePermissions && hasSafPermissions) {
                    Log.d(TAG, "All permissions granted in onResume - setting up UI");

                    // ✅ Dismiss any existing permission bottom sheets with null safety
                    try {
                        if (getSupportFragmentManager() != null && this != null && !isFinishing() && !isDestroyed()) {
                            boolean hasRuntimeDialog = getSupportFragmentManager().findFragmentByTag(RuntimePermissionBottomSheetFragment.TAG) != null;
                            boolean hasSafDialog = getSupportFragmentManager().findFragmentByTag(SafPermissionBottomSheetFragment.TAG) != null;

                            if (hasRuntimeDialog || hasSafDialog) {
                                Log.d(TAG, "Dismissing permission dialogs as permissions are now granted");

                                if (hasRuntimeDialog) {
                                    try {
                                        RuntimePermissionBottomSheetFragment fragment = (RuntimePermissionBottomSheetFragment)
                                            getSupportFragmentManager().findFragmentByTag(RuntimePermissionBottomSheetFragment.TAG);
                                        if (fragment != null && fragment.isAdded() && !fragment.isDetached()) {
                                            fragment.dismiss();
                                        }
                                    } catch (Exception e) {
                                        Log.e(TAG, "Error dismissing runtime permission dialog: " + e.getMessage(), e);
                                    }
                                }

                                if (hasSafDialog) {
                                    try {
                                        SafPermissionBottomSheetFragment fragment = (SafPermissionBottomSheetFragment)
                                            getSupportFragmentManager().findFragmentByTag(SafPermissionBottomSheetFragment.TAG);
                                        if (fragment != null && fragment.isAdded() && !fragment.isDetached()) {
                                            fragment.dismiss();
                                        }
                                    } catch (Exception e) {
                                        Log.e(TAG, "Error dismissing SAF permission dialog: " + e.getMessage(), e);
                                    }
                                }
                            }
                        } else {
                            Log.w(TAG, "Fragment manager is null or activity is invalid - cannot dismiss permission dialogs");
                        }

                        // Reset flag if user came back from settings
                        if (userWentToSettings) {
                            userWentToSettings = false; // Reset flag
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error checking/dismissing permission dialogs: " + e.getMessage(), e);
                    }

                    // Setup UI if not already done
                    try {
                        if (viewPager != null && viewPager.getAdapter() == null) {
                            setupViewPagerAndTabs();
                        }

                        // ✅ Use protected method when returning from settings
                        if (mainViewModel != null) {
                            Log.d(TAG, "Returned from settings - loading ONLY Saved tab (Images/Videos unchanged)");
                            isHandlingMediaPermissionChange = true;
                            mainViewModel.setSkipImagesVideosRefresh(true);
                            mainViewModel.loadSavedMediaOnly();
                            mainViewModel.setSkipImagesVideosRefresh(false);
                            isHandlingMediaPermissionChange = false;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error setting up UI after permission grant: " + e.getMessage(), e);
                    }
                } else {
                    // Permissions still missing, continue with normal permission flow
                    Log.d(TAG, "Some permissions still missing in onResume - continuing permission flow");
                    handlePermissionsAndInitialLoad();
                }

            } catch (Exception e) {
                Log.e(TAG, "Error handling permissions in onResume: " + e.getMessage(), e);
                // Fallback to normal permission handling
                try {
                    handlePermissionsAndInitialLoad();
                } catch (Exception fallbackError) {
                    Log.e(TAG, "Fallback permission handling also failed: " + fallbackError.getMessage(), fallbackError);
                }
            }

            // Ensure data is loaded when returning from preview (only if UI is set up)
            try {
                if (mainViewModel != null && viewPager != null && viewPager.getAdapter() != null) {
                    Log.d(TAG, "MainActivity onResume - ensuring data is loaded for preview return");
                    // Trigger a refresh to ensure all tabs have current data
                    mainViewModel.loadAllMedia();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error loading data in onResume: " + e.getMessage(), e);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in onResume: " + e.getMessage(), e);
        }
    }



    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }



    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        try {
            if (item == null) {
                return super.onOptionsItemSelected(item);
            }

            int id = item.getItemId();
            if (id == R.id.action_refresh) {
                // Refresh based on active tab
                try {
                    if (viewPager != null && mainViewModel != null) {
                        int position = viewPager.getCurrentItem();
                        MainPagerAdapter adapter = (MainPagerAdapter) viewPager.getAdapter();
                        // Always handle all 3 tabs
                        switch (position) {
                            case 0: // Images
                                mainViewModel.loadWhatsAppStatuses();
                                break;
                            case 1: // Videos
                                mainViewModel.loadWhatsAppStatuses();
                                break;
                            case 2: // Saved
                                mainViewModel.loadSavedMedia();
                                break;
                        }
                    } else {
                        Log.w(TAG, "ViewPager or MainViewModel is null, cannot refresh");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error refreshing data: " + e.getMessage(), e);
                }
                return true;
            } else if (id == R.id.action_settings) {
                try {
                    // Open settings activity (to be created)
                    Intent intent = new Intent(this, SettingsActivity.class);
                    startActivity(intent);
                } catch (Exception e) {
                    Log.e(TAG, "Error opening settings: " + e.getMessage(), e);
                }
                return true;
            }
            return super.onOptionsItemSelected(item);
        } catch (Exception e) {
            Log.e(TAG, "Error in onOptionsItemSelected: " + e.getMessage(), e);
            return super.onOptionsItemSelected(item);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        // Destroy native ad to prevent memory leaks
        if (nativeAdContainer != null) {
            NativeAdHelper.destroyNativeAd(nativeAdContainer);
        }

        super.onDestroy();
        Log.d(TAG, "MainActivity destroyed");

        // Clear Glide memory to prevent leaks
        if (!isFinishing()) {
            com.bumptech.glide.Glide.get(this).clearMemory();
        }
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        Log.w(TAG, "Low memory warning - clearing Glide cache");

        // Clear Glide memory when system is low on memory
        com.bumptech.glide.Glide.get(this).clearMemory();

        // Clear disk cache in background
        new Thread(() -> com.bumptech.glide.Glide.get(this).clearDiskCache()).start();
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        Log.w(TAG, "Memory trim requested - level: " + level);

        // Clear Glide memory on memory pressure
        if (level >= TRIM_MEMORY_MODERATE) {
            com.bumptech.glide.Glide.get(this).clearMemory();
        }
    }

    /**
     * ✅ Setup Native Ad - Enhanced AdMob Pattern
     * Initialize and load native ad for bottom of home screen
     */
    private void setupNativeAd() {
        Log.d(TAG, "🎯 Setting up native ad for home screen");

        // Initialize Mobile Ads SDK (if not already done)
        MobileAds.initialize(this, initializationStatus -> {
            Log.d(TAG, "✅ AdMob SDK initialized in MainActivity");
        });

        // Get native ad container
        nativeAdContainer = findViewById(R.id.native_ad_container);

        if (nativeAdContainer != null) {
            // Load native ad using helper
            NativeAdHelper.loadNativeAd(this, nativeAdContainer, "Home");

            Log.d(TAG, "🔄 Native ad loading with unit ID: ca-app-pub-7557152164205920/5890575132");
        } else {
            Log.w(TAG, "❌ Native ad container not found in layout");
        }
    }



}