package com.ks.app.service.statussaver.ui.images;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.facebook.shimmer.Shimmer;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.ui.adapters.ShimmerAdapter;
import com.ks.app.service.statussaver.data.models.MediaItem;
import com.ks.app.service.statussaver.ui.main.MainViewModel;
import com.ks.app.service.statussaver.ui.adapters.OptimizedMediaAdapter;
import com.ks.app.service.statussaver.utils.FileUtils;
import com.ks.app.service.statussaver.utils.GridUtils;
import java.util.List;

/**
 * Optimized ImageTabFragment using Paging 3 for efficient large dataset handling
 * Follows Google's best practices for performance and memory management
 */
public class OptimizedImageTabFragment extends Fragment implements
        OptimizedMediaAdapter.OnMediaItemClickListener,
        OptimizedMediaAdapter.OnDownloadClickListener {

    private static final String TAG = "OptimizedImageTabFragment";

    private RecyclerView recyclerView;
    private OptimizedMediaAdapter adapter;
    private MainViewModel viewModel;
    private TextView emptyView;
    private SwipeRefreshLayout swipeRefreshLayout;
    private ShimmerFrameLayout shimmerLayout;
    private RecyclerView shimmerRecyclerView;
    private ShimmerAdapter shimmerAdapter;
    private boolean isFirstLoad = true;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_media_tab, container, false);

        // Initialize views
        recyclerView = view.findViewById(R.id.recycler_view);
        emptyView = view.findViewById(R.id.text_view_no_media);
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh_layout);
        shimmerLayout = view.findViewById(R.id.shimmer_view_container);
        shimmerRecyclerView = view.findViewById(R.id.shimmer_recycler_view);

        // Hide empty view by default - only show after data load confirms it's empty
        if (emptyView != null) {
            emptyView.setVisibility(View.GONE);
        }
        
        setupRecyclerView();
        setupShimmerRecyclerView();
        setupSwipeRefresh();

        // Set specific empty message for images
        emptyView.setText(R.string.no_images_found);

        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        viewModel = new ViewModelProvider(requireActivity()).get(MainViewModel.class);
        observeViewModel();
    }

    private void setupRecyclerView() {
        // Create optimized adapter
        adapter = new OptimizedMediaAdapter(getContext(), this, this, true);

        // Setup optimized layout manager
        recyclerView.setLayoutManager(GridUtils.createOptimizedGridLayoutManager(getContext()));

        // Apply performance optimizations
        GridUtils.optimizeRecyclerView(recyclerView);

        // Set adapter
        recyclerView.setAdapter(adapter);
    }

    private void setupShimmerRecyclerView() {
        // Create optimized shimmer configuration
        Shimmer shimmer = new Shimmer.AlphaHighlightBuilder()
                .setDuration(1000)                    // Faster animation duration for better performance
                .setBaseAlpha(0.8f)                   // Higher base alpha to reduce overdraw
                .setHighlightAlpha(1.0f)              // Full highlight for better contrast
                .setDirection(Shimmer.Direction.LEFT_TO_RIGHT)
                .setAutoStart(false)                  // Manual control for better performance
                .build();

        // Apply optimized shimmer to container
        shimmerLayout.setShimmer(shimmer);

        // Create shimmer adapter
        shimmerAdapter = new ShimmerAdapter();

        // Setup layout manager for shimmer (same as main RecyclerView)
        shimmerRecyclerView.setLayoutManager(GridUtils.createOptimizedGridLayoutManager(getContext()));

        // Set shimmer adapter
        shimmerRecyclerView.setAdapter(shimmerAdapter);
    }
    
    private void setupSwipeRefresh() {
        swipeRefreshLayout.setOnRefreshListener(() -> {
            Log.d(TAG, "Swipe to refresh triggered for images.");
            isFirstLoad = false; // Mark as refresh, not first load
            if (viewModel != null) {
                viewModel.loadWhatsAppStatuses();
            }
        });
        swipeRefreshLayout.setColorSchemeResources(R.color.colorPrimary);

        // Add scroll listener to prevent unwanted refresh
        setupScrollListener();
    }

    private void setupScrollListener() {
        if (recyclerView != null && swipeRefreshLayout != null) {
            recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
                @Override
                public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                    super.onScrolled(recyclerView, dx, dy);

                    // Check if we're at the top of the list
                    boolean isAtTop = !recyclerView.canScrollVertically(-1);

                    // Only enable swipe refresh when at the top
                    swipeRefreshLayout.setEnabled(isAtTop);
                }
            });
        }
    }

    private void observeViewModel() {
        // Observe image data
        viewModel.getImageStatuses().observe(getViewLifecycleOwner(), mediaItems -> {
            Log.d(TAG, "Received new data for images: " + (mediaItems != null ? mediaItems.size() : 0));
            adapter.updateItems(mediaItems);
            updateEmptyViewVisibility();
        });
        
        // Observe loading state
        viewModel.getIsLoadingImages().observe(getViewLifecycleOwner(), isLoading -> {
            if (isLoading != null) {
                handleLoadingState(isLoading);

                // Show/hide empty view based on adapter state
                if (!isLoading) {
                    updateEmptyViewVisibility();
                    isFirstLoad = false; // Mark first load as complete
                }
            }
        });
        
        // Observe error messages
        viewModel.getErrorMessage().observe(getViewLifecycleOwner(), errorMessage -> {
            if (errorMessage != null && !errorMessage.isEmpty()) {
                Toast.makeText(getContext(), errorMessage, Toast.LENGTH_SHORT).show();
                viewModel.clearErrorMessage();
            }
        });
        
        // Observe toast messages
        viewModel.getToastMessage().observe(getViewLifecycleOwner(), message -> {
            if (message != null && !message.isEmpty()) {
                Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
                viewModel.clearToastMessage();
            }
        });
    }
    
    private void handleLoadingState(boolean isLoading) {
        if (isFirstLoad && isLoading) {
            // First load: Show shimmer, hide refresh loader
            showShimmer();
            swipeRefreshLayout.setRefreshing(false);
        } else if (!isFirstLoad && isLoading) {
            // Refresh: Show refresh loader, hide shimmer
            hideShimmer();
            swipeRefreshLayout.setRefreshing(true);
        } else {
            // Not loading: Hide both shimmer and refresh loader
            hideShimmer();
            swipeRefreshLayout.setRefreshing(false);
        }
    }

    private void showShimmer() {
        if (shimmerLayout != null) {
            shimmerLayout.setVisibility(View.VISIBLE);
            shimmerLayout.startShimmer();
            Log.d(TAG, "Shimmer started for images");
        }
        if (recyclerView != null) {
            recyclerView.setVisibility(View.GONE);
        }
        // Always hide empty view when showing shimmer
        if (emptyView != null) {
            emptyView.setVisibility(View.GONE);
        }
    }

    private void hideShimmer() {
        if (shimmerLayout != null) {
            // Stop shimmer ASAP to free resources
            shimmerLayout.stopShimmer();
            shimmerLayout.setVisibility(View.GONE);
            Log.d(TAG, "Shimmer stopped for images");
        }
    }

    private void updateEmptyViewVisibility() {
        // Only show empty view if we're not loading and not on first load
        boolean isEmpty = adapter.isEmpty();
        boolean shouldShowEmpty = isEmpty && !isFirstLoad;

        if (emptyView != null) {
            emptyView.setVisibility(shouldShowEmpty ? View.VISIBLE : View.GONE);
            if (shouldShowEmpty) {
                emptyView.setText(R.string.no_images_found);
            }
        }

        if (recyclerView != null) {
            recyclerView.setVisibility(isEmpty ? View.GONE : View.VISIBLE);
        }
    }

    @Override
    public void onMediaItemClick(MediaItem mediaItem) {
        Intent intent = new Intent(getActivity(), ImagePreviewActivity.class);
        intent.putExtra("media_uri", mediaItem.getUri().toString());
        intent.putExtra("is_video", mediaItem.isVideo());
        intent.putExtra("file_name", mediaItem.getFileName());
        intent.putExtra("show_download_button", true);
        startActivity(intent);
    }

    @Override
    public void onDownloadClick(MediaItem mediaItem) {
        FileUtils.saveStatus(getContext(), mediaItem.getUri(), mediaItem.getFileName(), mediaItem.isVideo());
        Toast.makeText(getContext(), "Downloading " + mediaItem.getFileName(), Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "OptimizedImageTabFragment onResume");

        // Ensure data is displayed when fragment becomes visible
        handleFragmentVisible();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        Log.d(TAG, "setUserVisibleHint: " + isVisibleToUser);

        if (isVisibleToUser && isViewCreated()) {
            handleFragmentVisible();
        }
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        Log.d(TAG, "onHiddenChanged: hidden=" + hidden);

        if (!hidden && isViewCreated()) {
            handleFragmentVisible();
        }
    }

    private boolean isViewCreated() {
        return getView() != null && adapter != null;
    }

    private void handleFragmentVisible() {
        Log.d(TAG, "Fragment became visible, ensuring data display");

        if (!isViewCreated()) {
            Log.d(TAG, "View not ready, skipping");
            return;
        }

        // Force update UI state
        updateEmptyViewVisibility();

        // Ensure current data is displayed
        if (viewModel != null) {
            List<MediaItem> currentData = viewModel.getImageStatuses().getValue();
            if (currentData != null && adapter != null) {
                Log.d(TAG, "Displaying current data: " + currentData.size() + " items");
                adapter.updateItems(currentData);
            } else if (adapter != null && adapter.isEmpty()) {
                Log.d(TAG, "No data available, refreshing");
                viewModel.loadWhatsAppStatuses();
            }
        }

        updateEmptyViewVisibility();
    }
}
