<?xml version="1.0" encoding="utf-8"?>
<!-- ✅ FIXED: Non-Overlapping Shimmer Layout -->
<com.facebook.shimmer.ShimmerFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/shimmer_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="120dp"
    android:layout_margin="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/ad_background"
        android:padding="16dp">

        <!-- ✅ AD ATTRIBUTION SHIMMER - Top Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <View
                android:layout_width="24dp"
                android:layout_height="16dp"
                android:background="@drawable/shimmer_rectangle" />

        </LinearLayout>

        <!-- ✅ Main Content Row - No Overlapping -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- App Icon Shimmer -->
            <View
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/shimmer_circle" />

            <!-- Content Section Shimmer -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="12dp"
                android:orientation="vertical">

                <!-- Headline Shimmer -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="14dp"
                    android:background="@drawable/shimmer_rectangle"
                    android:layout_marginBottom="4dp" />

                <!-- Body Text Shimmer -->
                <View
                    android:layout_width="200dp"
                    android:layout_height="12dp"
                    android:background="@drawable/shimmer_rectangle"
                    android:layout_marginBottom="4dp" />

                <!-- Rating Shimmer -->
                <View
                    android:layout_width="60dp"
                    android:layout_height="12dp"
                    android:background="@drawable/shimmer_rectangle" />

            </LinearLayout>

            <!-- CTA Button Shimmer -->
            <View
                android:layout_width="70dp"
                android:layout_height="36dp"
                android:background="@drawable/shimmer_button" />

        </LinearLayout>

    </LinearLayout>

</com.facebook.shimmer.ShimmerFrameLayout>
