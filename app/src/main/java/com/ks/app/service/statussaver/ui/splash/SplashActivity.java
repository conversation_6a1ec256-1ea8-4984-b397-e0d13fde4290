package com.ks.app.service.statussaver.ui.splash;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.ui.main.MainActivity;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.AdError;
import androidx.annotation.NonNull;

public class SplashActivity extends AppCompatActivity {

    private static final String TAG = "SplashActivity";
    private static final int SPLASH_DURATION = 3000; // 3 seconds - minimum splash time
    private static final int MAX_AD_WAIT_TIME = 8000; // Maximum 8 seconds total wait for ad

    // Your production interstitial ad unit ID
    private static final String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-7557152164205920/6418571631";

    private InterstitialAd mInterstitialAd;
    private boolean splashTimerFinished = false;
    private boolean adLoaded = false;
    private boolean adLoadingCompleted = false; // Track if ad loading finished (success or failure)
    private boolean adShown = false;
    private long adLoadStartTime = 0;

    // Handler references for proper cleanup
    private Handler mainHandler;
    private Runnable splashTimerRunnable;
    private Runnable timeoutRunnable;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        // Hide the action bar for full screen splash
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        // Initialize views
        ImageView splashIcon = findViewById(R.id.splash_icon);
        TextView appName = findViewById(R.id.splash_app_name);
        ProgressBar loadingIndicator = findViewById(R.id.splash_loading_indicator);

        // Create fade-in animation
        AlphaAnimation fadeIn = new AlphaAnimation(0.0f, 1.0f);
        fadeIn.setDuration(1000); // 1 second fade-in
        fadeIn.setStartOffset(200); // Start after 200ms

        // Apply animations
        splashIcon.startAnimation(fadeIn);
        appName.startAnimation(fadeIn);
        loadingIndicator.startAnimation(fadeIn);

        Log.d(TAG, "🚀 STRICT APPROACH: Starting 3-second splash with background ad loading");

        // ✅ Initialize Handler for proper cleanup
        mainHandler = new Handler(Looper.getMainLooper());

        // ✅ Initialize Mobile Ads SDK immediately for background loading
        MobileAds.initialize(this, initializationStatus -> {
            Log.d(TAG, "✅ AdMob SDK initialized - ready for background ad loading");
        });

        // ✅ Start loading interstitial ad in background immediately
        Log.d(TAG, "🔄 Starting background interstitial ad loading during 3-second splash...");
        loadInterstitialAdInBackground();

        // ✅ Create splash timer runnable
        splashTimerRunnable = new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "⏰ TIMER COMPLETE: 3 seconds completed - checking if ad loading is also complete");
                splashTimerFinished = true;
                checkAdStatusAndProceed();
            }
        };

        // ✅ Create timeout runnable
        timeoutRunnable = new Runnable() {
            @Override
            public void run() {
                if (!adLoadingCompleted) {
                    Log.w(TAG, "⏰ TIMEOUT: Maximum wait time reached - forcing ad loading completion");
                    Log.w(TAG, "🚨 EMERGENCY FALLBACK: Proceeding without ad after " + MAX_AD_WAIT_TIME + "ms");

                    // Force completion and proceed
                    adLoadingCompleted = true;
                    adLoaded = false;
                    mInterstitialAd = null;

                    checkAdStatusAndProceed();
                } else {
                    Log.d(TAG, "⏰ TIMEOUT SKIPPED: Ad loading already completed, timeout not needed");
                }
            }
        };

        // ✅ Start timers with proper references for cleanup
        mainHandler.postDelayed(splashTimerRunnable, SPLASH_DURATION);
        mainHandler.postDelayed(timeoutRunnable, MAX_AD_WAIT_TIME);
    }

    /**
     * ✅ Load Interstitial Ad in Background - Optimized for Splash Screen
     * Starts loading immediately when splash screen appears for faster ad delivery
     */
    private void loadInterstitialAdInBackground() {
        adLoadStartTime = System.currentTimeMillis();
        Log.d(TAG, "🚀 BACKGROUND LOADING: Starting interstitial ad load during splash screen");
        Log.d(TAG, "📱 Ad Unit ID: " + INTERSTITIAL_AD_UNIT_ID);
        Log.d(TAG, "⏰ Load start time: " + adLoadStartTime);

        // Create ad request
        AdRequest adRequest = new AdRequest.Builder().build();

        // Start loading ad in background
        InterstitialAd.load(this, INTERSTITIAL_AD_UNIT_ID, adRequest,
            new InterstitialAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.d(TAG, "✅ AD LOADED SUCCESSFULLY: Interstitial ad loaded during background loading!");
                    Log.d(TAG, "⚡ Load time: " + loadTime + "ms (Target: within " + SPLASH_DURATION + "ms)");

                    mInterstitialAd = interstitialAd;
                    adLoaded = true;
                    adLoadingCompleted = true; // Mark ad loading as completed successfully

                    if (loadTime < SPLASH_DURATION) {
                        Log.d(TAG, "🚀 PERFECT TIMING: Ad loaded in " + loadTime + "ms (within 3-second window)");
                        Log.d(TAG, "⏳ WAITING FOR TIMER: Keeping splash visible until 3-second minimum...");
                    } else {
                        Log.d(TAG, "⏰ SLOW LOADING: Ad took " + loadTime + "ms (longer than 3 seconds)");
                        Log.d(TAG, "🎯 READY TO PROCEED: Will check if timer is also finished");
                    }

                    // Check if BOTH conditions are met (timer + ad loading)
                    if (splashTimerFinished) {
                        Log.d(TAG, "✅ BOTH CONDITIONS MET: Timer finished and ad loaded - proceeding now");
                        checkAdStatusAndProceed();
                    } else {
                        Log.d(TAG, "⏳ WAITING FOR TIMER: Ad ready, but keeping splash visible until timer completes");
                    }
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError adError) {
                    long loadTime = System.currentTimeMillis() - adLoadStartTime;
                    Log.w(TAG, "❌ AD FAILED TO LOAD: Interstitial ad failed during background loading");
                    Log.w(TAG, "⏰ Failed after: " + loadTime + "ms");
                    Log.w(TAG, "📊 Error details: " + adError.getMessage());
                    Log.w(TAG, "🔢 Error code: " + adError.getCode());
                    Log.w(TAG, "🌐 Error domain: " + adError.getDomain());

                    mInterstitialAd = null;
                    adLoaded = false; // Ad failed to load
                    adLoadingCompleted = true; // Mark ad loading as completed (with failure)

                    Log.d(TAG, "⏳ WAITING FOR TIMER: Ad failed, but keeping splash visible until 3-second minimum");

                    // Check if BOTH conditions are met (timer + ad loading completed)
                    if (splashTimerFinished) {
                        Log.d(TAG, "✅ TIMER FINISHED: 3 seconds passed and ad loading completed (failed) - proceeding without ad");
                        checkAdStatusAndProceed();
                    } else {
                        Log.d(TAG, "⏳ KEEPING SPLASH VISIBLE: Waiting for 3-second timer to complete before proceeding");
                    }
                }
            });
    }

    /**
     * ✅ Check Ad Status and Proceed - STRICT Logic for Splash + Ad Loading
     * Waits for BOTH minimum splash time AND ad loading to complete before hiding splash
     * Only proceeds when BOTH conditions are met
     */
    private void checkAdStatusAndProceed() {
        Log.d(TAG, "🔍 CHECKING STATUS:");
        Log.d(TAG, "   📱 Splash timer finished: " + splashTimerFinished);
        Log.d(TAG, "   📺 Ad loaded successfully: " + adLoaded);
        Log.d(TAG, "   ✅ Ad loading completed: " + adLoadingCompleted);

        if (splashTimerFinished && adLoadingCompleted) {
            // BOTH conditions met - clear timeout and proceed
            Log.d(TAG, "🧹 CLEARING TIMEOUT: Both conditions met, removing timeout handler");
            clearTimeoutHandler();

            if (adLoaded) {
                Log.d(TAG, "✅ BOTH CONDITIONS MET: 3 seconds passed AND ad loaded successfully");
                Log.d(TAG, "🎯 HIDING SPLASH: Now hiding splash screen and showing ad");
            } else {
                Log.d(TAG, "✅ BOTH CONDITIONS MET: 3 seconds passed AND ad loading completed (failed)");
                Log.d(TAG, "🎯 HIDING SPLASH: Now hiding splash screen and going to main activity");
            }
            showInterstitialAdOrProceed();
        } else if (splashTimerFinished && !adLoadingCompleted) {
            // Timer finished but ad still loading - KEEP SPLASH VISIBLE
            Log.d(TAG, "⏳ WAITING FOR AD: 3 seconds passed, but ad still loading...");
            Log.d(TAG, "👁️ SPLASH STAYS VISIBLE: Keeping splash screen visible until ad loading completes");
            // Do nothing - keep waiting for ad loading to complete
        } else if (!splashTimerFinished && adLoadingCompleted) {
            // Ad loading completed but timer not finished - KEEP SPLASH VISIBLE
            if (adLoaded) {
                Log.d(TAG, "⏳ WAITING FOR TIMER: Ad loaded successfully, but waiting for 3-second minimum...");
            } else {
                Log.d(TAG, "⏳ WAITING FOR TIMER: Ad failed to load, but waiting for 3-second minimum...");
            }
            Log.d(TAG, "👁️ SPLASH STAYS VISIBLE: Keeping splash screen visible until timer completes");
            // Do nothing - keep waiting for timer
        } else {
            // Neither condition met - KEEP SPLASH VISIBLE
            Log.d(TAG, "⏳ WAITING FOR BOTH: Timer and ad loading both still in progress...");
            Log.d(TAG, "👁️ SPLASH STAYS VISIBLE: Waiting for both conditions to be met");
        }
    }

    /**
     * ✅ Show Interstitial Ad or Proceed - Enhanced with Background Loading Status
     * Shows ad if background loading succeeded, otherwise goes directly to MainActivity
     */
    private void showInterstitialAdOrProceed() {
        Log.d(TAG, "🏁 READY TO PROCEED: Both splash time and ad loading are complete!");

        if (mInterstitialAd != null && !adShown) {
            Log.d(TAG, "🎯 SHOWING INTERSTITIAL AD: Ad loaded successfully during splash");
            Log.d(TAG, "⏳ Waiting for user to close ad before navigating to Home...");

            adShown = true; // Mark ad as shown to prevent multiple displays

            // Set up full screen content callback - ONLY navigate after user action
            mInterstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    Log.d(TAG, "✅ USER CLOSED AD: User dismissed interstitial ad");
                    Log.d(TAG, "🧹 CLEARING ALL ADS: Unloading all ads from memory");

                    // Clear all ads from memory before navigation
                    clearAllAdsFromMemory();

                    Log.d(TAG, "🏠 NAVIGATING TO HOME: Going to MainActivity now");
                    navigateToMainActivity();
                }

                @Override
                public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                    Log.w(TAG, "❌ FAILED TO SHOW AD: " + adError.getMessage());
                    Log.d(TAG, "🧹 CLEARING ALL ADS: Unloading all ads from memory");

                    // Clear all ads from memory before navigation
                    clearAllAdsFromMemory();

                    Log.d(TAG, "🏠 NAVIGATING TO HOME: Going to MainActivity without ad");
                    navigateToMainActivity();
                }

                @Override
                public void onAdShowedFullScreenContent() {
                    Log.d(TAG, "📺 AD DISPLAYED: Interstitial ad is now showing to user");
                    Log.d(TAG, "⏳ Waiting for user to close ad...");
                }

                @Override
                public void onAdClicked() {
                    Log.d(TAG, "👆 USER CLICKED AD: User interacted with the ad");
                    // Note: Don't navigate here - wait for dismiss
                }

                @Override
                public void onAdImpression() {
                    Log.d(TAG, "👁️ AD IMPRESSION: Ad impression recorded for revenue");
                }
            });

            // Show the ad that was loaded in background
            Log.d(TAG, "🚀 DISPLAYING AD: Showing interstitial ad to user now");
            mInterstitialAd.show(this);

        } else if (adShown) {
            Log.d(TAG, "⚠️ AD ALREADY SHOWN: Interstitial ad was already displayed");
            Log.d(TAG, "🏠 NAVIGATING TO HOME: Going to MainActivity");
            navigateToMainActivity();

        } else {
            Log.d(TAG, "⚠️ NO AD AVAILABLE: No interstitial ad to show (loading failed)");
            Log.d(TAG, "🧹 CLEARING ALL ADS: Unloading all ads from memory");

            // Clear all ads from memory before navigation
            clearAllAdsFromMemory();

            Log.d(TAG, "🏠 NAVIGATING TO HOME: Going directly to MainActivity");
            navigateToMainActivity();
        }
    }

    /**
     * ✅ Clear All Ads From Memory - Complete Ad Cleanup
     * Clears interstitial ads, native ad cache, and any other ad-related memory
     * Called before navigating to MainActivity to ensure clean state
     */
    private void clearAllAdsFromMemory() {
        Log.d(TAG, "🧹 STARTING AD CLEANUP: Clearing all ads from memory");

        // 1. Clear interstitial ad
        if (mInterstitialAd != null) {
            Log.d(TAG, "🗑️ Clearing interstitial ad");
            mInterstitialAd = null;
        }

        // 2. Clear all cached native ads
        try {
            // Import NativeAdHelper if not already imported
            com.ks.app.service.statussaver.utils.NativeAdHelper.clearAllCachedAds();
            Log.d(TAG, "🗑️ Cleared all cached native ads");
        } catch (Exception e) {
            Log.w(TAG, "⚠️ Error clearing native ad cache: " + e.getMessage());
        }

        // 3. Force garbage collection to free memory
        System.gc();

        Log.d(TAG, "✅ AD CLEANUP COMPLETE: All ads cleared from memory");
        Log.d(TAG, "💾 Memory freed for optimal MainActivity performance");
    }

    /**
     * ✅ Navigate to Main Activity (Home Screen)
     * Final navigation to MainActivity - only called after ad cleanup
     */
    private void navigateToMainActivity() {
        Log.d(TAG, "🏠 FINAL NAVIGATION: Starting MainActivity (Home Screen)");
        Log.d(TAG, "✅ FLOW COMPLETE: Splash → Ad Loading → Ad Display → User Action → Home");

        Intent intent = new Intent(SplashActivity.this, MainActivity.class);
        startActivity(intent);

        // Close splash activity
        finish();

        // Add smooth transition animation
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

        Log.d(TAG, "🎉 SUCCESS: User is now in MainActivity (Home Screen)");
    }

    /**
     * ✅ Clear Timeout Handler - Prevents unnecessary timeout execution
     * Called when both conditions are met to clean up pending timeout
     */
    private void clearTimeoutHandler() {
        if (mainHandler != null && timeoutRunnable != null) {
            Log.d(TAG, "🧹 TIMEOUT CLEARED: Removing timeout handler to prevent unnecessary execution");
            mainHandler.removeCallbacks(timeoutRunnable);
        }
    }

    /**
     * ✅ Clear All Handlers - Complete cleanup of all pending handlers
     * Called in onDestroy to prevent memory leaks
     */
    private void clearAllHandlers() {
        if (mainHandler != null) {
            Log.d(TAG, "🧹 CLEANUP: Removing all pending handlers");
            if (splashTimerRunnable != null) {
                mainHandler.removeCallbacks(splashTimerRunnable);
            }
            if (timeoutRunnable != null) {
                mainHandler.removeCallbacks(timeoutRunnable);
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        Log.d(TAG, "🧹 ACTIVITY DESTROY: Cleaning up all resources");

        // Clear all handlers to prevent memory leaks
        clearAllHandlers();

        // Clear all ads from memory
        clearAllAdsFromMemory();

        Log.d(TAG, "✅ DESTROY CLEANUP COMPLETE: All resources and ads cleaned up");
    }

    @Override
    public void onBackPressed() {
        // Disable back button during splash screen and ad display
        Log.d(TAG, "🚫 BACK BUTTON DISABLED: User tried to go back during splash/ad");
        Log.d(TAG, "⏳ Please wait for the process to complete...");
        // Do nothing - prevent user from interrupting the flow
    }
}
