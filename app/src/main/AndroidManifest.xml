<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.ks.app.service.statussaver"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Media permissions for status access -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- Internet permission required for AdMob -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:name=".SaveProApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SmartStatusSaver"
        tools:targetApi="31">

        <!-- AdMob App ID (Replace with your actual AdMob App ID) -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-7557152164205920~6417975169"/>

        <!-- Splash Activity - Launcher -->
        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.SmartStatusSaver.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Main Activity -->
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="false" />
        <activity android:name=".ui.images.ImagePreviewActivity" />
        <activity android:name=".ui.videos.VideoPreviewActivity" />
        <activity android:name=".ui.main.SettingsActivity" />
        <activity android:name=".ui.settings.PrivacyPolicyActivity" />
        <activity android:name=".ui.settings.ContactUsActivity" />
        <activity android:name=".ui.settings.AboutActivity" />
    </application>

</manifest>