<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center_horizontal"
    android:background="@drawable/permission_dialog_background"
    app:behavior_hideable="true"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <!-- Title -->
    <TextView
        android:id="@+id/bottom_sheet_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/access_permission_title"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
        android:textColor="@android:color/white"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Message -->
    <TextView
        android:id="@+id/bottom_sheet_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/access_permission_message"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
        android:textColor="@android:color/white"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Primary Button -->
    <Button
        android:id="@+id/button_allow_access"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/access_permission_button_allow"
        android:textColor="@android:color/white"
        style="@style/Widget.MaterialComponents.Button" />

</LinearLayout>