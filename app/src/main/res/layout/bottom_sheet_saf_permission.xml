<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    app:behavior_hideable="true"
    app:behavior_peekHeight="0dp"
    app:behavior_fitToContents="false"
    app:behavior_expandedOffset="0dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <!-- Main Content ScrollView -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp"
            android:gravity="center_horizontal">

        <!-- Status Bar Spacer -->
        <View
            android:layout_width="match_parent"
            android:layout_height="24dp" />

        <!-- Title -->
        <TextView
            android:id="@+id/bottom_sheet_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Media Access Permission"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <!-- Subtitle -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Grant access to view and save WhatsApp statuses"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="#B3FFFFFF"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- Visual Guide Container -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardBackgroundColor="#1A1A1A"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">



                <!-- Folder Structure Visual Guide -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="#0D1117"
                    android:padding="12dp"
                    android:layout_marginBottom="12dp">

                    <!-- Breadcrumb Path -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="POCO X4 Pro 5G"
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" > "
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Android"
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" > "
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="media"
                            android:textColor="#58A6FF"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- Files in media Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Files in media"
                            android:textColor="@android:color/white"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="1dp"
                            android:layout_weight="1" />

                        <!-- Grid icon -->
                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_grid_view"
                            android:tint="#8B949E" />

                    </LinearLayout>

                    <!-- App Folder Items -->
                    <ScrollView
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginBottom="12dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <!-- Folder Item with Shimmer (Hidden Name) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <com.facebook.shimmer.ShimmerFrameLayout
                                    android:layout_width="180dp"
                                    android:layout_height="20dp"
                                    app:shimmer_duration="1200"
                                    app:shimmer_auto_start="true"
                                    app:shimmer_direction="left_to_right"
                                    app:shimmer_repeat_count="-1"
                                    app:shimmer_repeat_mode="restart"
                                    app:shimmer_base_color="#2A2A2A"
                                    app:shimmer_highlight_color="#4A4A4A">

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:background="#2A2A2A" />

                                </com.facebook.shimmer.ShimmerFrameLayout>

                            </LinearLayout>

                            <!-- Folder Item with Shimmer (Hidden Name) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <com.facebook.shimmer.ShimmerFrameLayout
                                    android:layout_width="160dp"
                                    android:layout_height="20dp"
                                    app:shimmer_duration="1300"
                                    app:shimmer_auto_start="true"
                                    app:shimmer_direction="left_to_right"
                                    app:shimmer_repeat_count="-1"
                                    app:shimmer_repeat_mode="restart"
                                    app:shimmer_base_color="#2A2A2A"
                                    app:shimmer_highlight_color="#4A4A4A">

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:background="#2A2A2A" />

                                </com.facebook.shimmer.ShimmerFrameLayout>

                            </LinearLayout>

                            <!-- Folder Item with Shimmer (Hidden Name) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <com.facebook.shimmer.ShimmerFrameLayout
                                    android:layout_width="200dp"
                                    android:layout_height="20dp"
                                    app:shimmer_duration="1100"
                                    app:shimmer_auto_start="true"
                                    app:shimmer_direction="left_to_right"
                                    app:shimmer_repeat_count="-1"
                                    app:shimmer_repeat_mode="restart"
                                    app:shimmer_base_color="#2A2A2A"
                                    app:shimmer_highlight_color="#4A4A4A">

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:background="#2A2A2A" />

                                </com.facebook.shimmer.ShimmerFrameLayout>

                            </LinearLayout>

                            <!-- Folder Item with Shimmer (Hidden Name) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <com.facebook.shimmer.ShimmerFrameLayout
                                    android:layout_width="170dp"
                                    android:layout_height="20dp"
                                    app:shimmer_duration="1400"
                                    app:shimmer_auto_start="true"
                                    app:shimmer_direction="left_to_right"
                                    app:shimmer_repeat_count="-1"
                                    app:shimmer_repeat_mode="restart"
                                    app:shimmer_base_color="#2A2A2A"
                                    app:shimmer_highlight_color="#4A4A4A">

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:background="#2A2A2A" />

                                </com.facebook.shimmer.ShimmerFrameLayout>

                            </LinearLayout>

                            <!-- Folder Item with Shimmer (Hidden Name) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <com.facebook.shimmer.ShimmerFrameLayout
                                    android:layout_width="250dp"
                                    android:layout_height="20dp"
                                    app:shimmer_duration="1000"
                                    app:shimmer_auto_start="true"
                                    app:shimmer_direction="left_to_right"
                                    app:shimmer_repeat_count="-1"
                                    app:shimmer_repeat_mode="restart"
                                    app:shimmer_base_color="#2A2A2A"
                                    app:shimmer_highlight_color="#4A4A4A">

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:background="#2A2A2A" />

                                </com.facebook.shimmer.ShimmerFrameLayout>

                            </LinearLayout>

                            <!-- Folder Item with Shimmer (Hidden Name) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:layout_marginBottom="1dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <com.facebook.shimmer.ShimmerFrameLayout
                                    android:layout_width="140dp"
                                    android:layout_height="20dp"
                                    app:shimmer_duration="1250"
                                    app:shimmer_auto_start="true"
                                    app:shimmer_direction="left_to_right"
                                    app:shimmer_repeat_count="-1"
                                    app:shimmer_repeat_mode="restart"
                                    app:shimmer_base_color="#2A2A2A"
                                    app:shimmer_highlight_color="#4A4A4A">

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:background="#2A2A2A" />

                                </com.facebook.shimmer.ShimmerFrameLayout>

                            </LinearLayout>

                            <!-- Folder Item with Shimmer (Hidden Name) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:padding="12dp"
                                android:background="#1A1A1A">

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_folder"
                                    android:tint="#8B949E"
                                    android:layout_marginEnd="12dp" />

                                <com.facebook.shimmer.ShimmerFrameLayout
                                    android:layout_width="190dp"
                                    android:layout_height="20dp"
                                    app:shimmer_duration="1350"
                                    app:shimmer_auto_start="true"
                                    app:shimmer_direction="left_to_right"
                                    app:shimmer_repeat_count="-1"
                                    app:shimmer_repeat_mode="restart"
                                    app:shimmer_base_color="#2A2A2A"
                                    app:shimmer_highlight_color="#4A4A4A">

                                    <View
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:background="#2A2A2A" />

                                </com.facebook.shimmer.ShimmerFrameLayout>

                            </LinearLayout>

                        </LinearLayout>

                    </ScrollView>

                </LinearLayout>

                <!-- Use This Folder Button with Hand Click Animation -->
                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <Button
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:text="USE THIS FOLDER"
                        android:textColor="#000000"
                        android:textStyle="bold"
                        android:background="#E6E6E6"
                        android:enabled="false"
                        android:textSize="14sp" />

                    <!-- Hand Click Animation -->
                    <ImageView
                        android:id="@+id/hand_click_animation"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_hand_hint"
                        android:layout_gravity="center|end"
                        android:layout_marginEnd="16dp"
                        android:alpha="0.9" />

                </FrameLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Instructions -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="After clicking 'Grant Access', navigate to:\nAndroid → media"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="#B3FFFFFF"
            android:gravity="center"
            android:lineSpacingExtra="4dp"
            android:layout_marginBottom="24dp" />

        <!-- Spacer to push button to bottom -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- Grant Access Button -->
        <Button
            android:id="@+id/button_allow_access"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="Grant Access"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            android:textSize="16sp"
            android:background="@drawable/cta_button_background"
            android:elevation="4dp"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp" />

        </LinearLayout>

    </ScrollView>



</FrameLayout>