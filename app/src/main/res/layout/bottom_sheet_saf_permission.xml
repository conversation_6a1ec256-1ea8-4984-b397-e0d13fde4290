<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/permission_dialog_background"
    app:behavior_hideable="true"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center_horizontal">

        <!-- Title -->
        <TextView
            android:id="@+id/bottom_sheet_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Media Access Permission"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <!-- Subtitle -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Grant access to view and save WhatsApp statuses"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="#B3FFFFFF"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- Visual Guide Container -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardBackgroundColor="#1A1A1A"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Guide Title -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Select WhatsApp folder:"
                    android:textColor="@android:color/white"
                    android:textStyle="bold"
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp" />

                <!-- Folder Structure Visual Guide -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="#0D1117"
                    android:padding="12dp"
                    android:layout_marginBottom="12dp">

                    <!-- Breadcrumb Path -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Android"
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" > "
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="media"
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" > "
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="com.whatsapp"
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=" > "
                            android:textColor="#8B949E"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="WhatsApp"
                            android:textColor="#58A6FF"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- Files in WhatsApp Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Files in WhatsApp"
                            android:textColor="@android:color/white"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="1dp"
                            android:layout_weight="1" />

                        <!-- List icon -->
                        <View
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:background="#333333" />

                    </LinearLayout>

                    <!-- Folder Items -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <!-- Backups Folder -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp"
                            android:background="#21262D"
                            android:layout_marginEnd="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_folder"
                                android:tint="#8B949E"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Backups"
                                android:textColor="@android:color/white"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <!-- Databases Folder -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="12dp"
                            android:background="#21262D"
                            android:layout_marginStart="8dp">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_folder"
                                android:tint="#8B949E"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Databases"
                                android:textColor="@android:color/white"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Media Folder (Highlighted) -->
                    <LinearLayout
                        android:layout_width="120dp"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="12dp"
                        android:background="#21262D"
                        android:layout_gravity="start">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_folder"
                            android:tint="#58A6FF"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Media"
                            android:textColor="@android:color/white"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Use This Folder Button (Visual Guide) -->
                <Button
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:text="USE THIS FOLDER"
                    android:textColor="#000000"
                    android:textStyle="bold"
                    android:background="#E6E6E6"
                    android:enabled="false"
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Instructions -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="After clicking 'Grant Access', navigate to:\nAndroid → media → com.whatsapp → WhatsApp\nThen tap 'USE THIS FOLDER'"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="#B3FFFFFF"
            android:gravity="center"
            android:lineSpacingExtra="4dp"
            android:layout_marginBottom="24dp" />

        <!-- Grant Access Button -->
        <Button
            android:id="@+id/button_allow_access"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="Grant Access"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            android:textSize="16sp"
            android:background="@drawable/cta_button_background"
            android:elevation="4dp" />

    </LinearLayout>

</ScrollView>