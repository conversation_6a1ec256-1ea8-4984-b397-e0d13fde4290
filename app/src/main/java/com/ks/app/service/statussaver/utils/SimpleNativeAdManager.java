package com.ks.app.service.statussaver.utils;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;

import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdLoader;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.nativead.NativeAd;
import com.google.android.gms.ads.nativead.NativeAdView;
import com.ks.app.service.statussaver.R;

/**
 * ✅ SIMPLE NATIVE AD MANAGER - Fresh Implementation
 * Clean, simple, and working native ad implementation
 */
public class SimpleNativeAdManager {
    
    private static final String TAG = "SimpleNativeAdManager";
    private static final String NATIVE_AD_UNIT_ID = "ca-app-pub-7557152164205920/5890575132";
    
    /**
     * ✅ Load Native Ad - Simple and Working
     * @param context Activity context
     * @param container FrameLayout to display the ad
     */
    public static void loadNativeAd(Context context, FrameLayout container) {
        if (context == null || container == null) {
            Log.w(TAG, "❌ Cannot load native ad - context or container is null");
            return;
        }

        Log.d(TAG, "🚀 Loading native ad...");
        Log.d(TAG, "📱 Ad Unit ID: " + NATIVE_AD_UNIT_ID);

        // Create AdLoader
        AdLoader adLoader = new AdLoader.Builder(context, NATIVE_AD_UNIT_ID)
                .forNativeAd(nativeAd -> {
                    Log.d(TAG, "✅ Native ad loaded successfully!");
                    displayNativeAd(context, container, nativeAd);
                })
                .withAdListener(new AdListener() {
                    @Override
                    public void onAdLoaded() {
                        Log.d(TAG, "📺 Ad loaded callback");
                    }

                    @Override
                    public void onAdFailedToLoad(LoadAdError adError) {
                        Log.w(TAG, "❌ Native ad failed to load");
                        Log.w(TAG, "Error: " + adError.getMessage());
                        Log.w(TAG, "Error code: " + adError.getCode());
                        
                        // Hide container on failure
                        container.setVisibility(View.GONE);
                    }

                    @Override
                    public void onAdClicked() {
                        Log.d(TAG, "👆 Native ad clicked");
                    }

                    @Override
                    public void onAdImpression() {
                        Log.d(TAG, "👁️ Native ad impression recorded");
                    }
                })
                .build();

        // Load the ad
        AdRequest adRequest = new AdRequest.Builder().build();
        adLoader.loadAd(adRequest);
    }

    /**
     * ✅ Display Native Ad - Populate and Show
     */
    private static void displayNativeAd(Context context, FrameLayout container, NativeAd nativeAd) {
        Log.d(TAG, "🎨 Displaying native ad...");

        // Inflate the native ad layout
        NativeAdView adView = (NativeAdView) LayoutInflater.from(context)
                .inflate(R.layout.native_ad_layout, null);

        // Populate the ad
        populateNativeAdView(nativeAd, adView);

        // Clear container and add the ad
        container.removeAllViews();
        container.addView(adView);
        container.setVisibility(View.VISIBLE);

        Log.d(TAG, "✅ Native ad displayed successfully!");
    }

    /**
     * ✅ Populate Native Ad View - Bind Data
     */
    private static void populateNativeAdView(NativeAd nativeAd, NativeAdView adView) {
        Log.d(TAG, "🔗 Populating native ad data...");

        // Set view references
        adView.setHeadlineView(adView.findViewById(R.id.ad_headline));
        adView.setBodyView(adView.findViewById(R.id.ad_body));
        adView.setCallToActionView(adView.findViewById(R.id.ad_call_to_action));
        adView.setIconView(adView.findViewById(R.id.ad_app_icon));
        adView.setStarRatingView(adView.findViewById(R.id.ad_stars));
        adView.setAdvertiserView(adView.findViewById(R.id.ad_advertiser));
        adView.setPriceView(adView.findViewById(R.id.ad_price));

        // Populate headline (required)
        if (nativeAd.getHeadline() != null) {
            ((TextView) adView.getHeadlineView()).setText(nativeAd.getHeadline());
        }

        // Populate body (optional)
        if (nativeAd.getBody() != null && !nativeAd.getBody().isEmpty()) {
            ((TextView) adView.getBodyView()).setText(nativeAd.getBody());
            adView.getBodyView().setVisibility(View.VISIBLE);
        } else {
            adView.getBodyView().setVisibility(View.GONE);
        }

        // Populate call to action (required)
        if (nativeAd.getCallToAction() != null) {
            ((Button) adView.getCallToActionView()).setText(nativeAd.getCallToAction());
        }

        // Populate icon
        if (nativeAd.getIcon() != null) {
            ((ImageView) adView.getIconView()).setImageDrawable(nativeAd.getIcon().getDrawable());
            adView.getIconView().setVisibility(View.VISIBLE);
        } else {
            adView.getIconView().setVisibility(View.GONE);
        }

        // Populate star rating
        if (nativeAd.getStarRating() != null) {
            ((RatingBar) adView.getStarRatingView()).setRating(nativeAd.getStarRating().floatValue());
            adView.getStarRatingView().setVisibility(View.VISIBLE);
        } else {
            adView.getStarRatingView().setVisibility(View.GONE);
        }

        // Populate advertiser
        if (nativeAd.getAdvertiser() != null && !nativeAd.getAdvertiser().isEmpty()) {
            ((TextView) adView.getAdvertiserView()).setText(nativeAd.getAdvertiser());
            adView.getAdvertiserView().setVisibility(View.VISIBLE);
        } else {
            adView.getAdvertiserView().setVisibility(View.GONE);
        }

        // Populate price
        if (nativeAd.getPrice() != null && !nativeAd.getPrice().isEmpty()) {
            ((TextView) adView.getPriceView()).setText(nativeAd.getPrice());
            adView.getPriceView().setVisibility(View.VISIBLE);
        } else {
            adView.getPriceView().setVisibility(View.GONE);
        }

        // Register the NativeAdView with the NativeAd object
        adView.setNativeAd(nativeAd);

        Log.d(TAG, "✅ Native ad data populated successfully!");
    }

    /**
     * ✅ Destroy Native Ad - Cleanup
     */
    public static void destroyNativeAd(FrameLayout container) {
        if (container != null) {
            container.removeAllViews();
            container.setVisibility(View.GONE);
            Log.d(TAG, "🗑️ Native ad destroyed");
        }
    }
}
