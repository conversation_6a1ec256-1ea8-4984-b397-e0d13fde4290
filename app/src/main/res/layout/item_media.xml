<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/media_item_margin"
    app:cardCornerRadius="@dimen/media_item_corner_radius"
    app:cardElevation="@dimen/media_item_elevation"
    android:foreground="?android:attr/selectableItemBackground">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Placeholder background with aspect ratio -->
        <View
            android:id="@+id/placeholder_background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/media_placeholder_background"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- Media type icon (image/video indicator) -->
        <ImageView
            android:id="@+id/icon_media_type"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_image_placeholder"
            android:tint="@color/media_icon_tint"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:contentDescription="@string/media_type_icon" />

        <!-- Actual thumbnail (hidden initially, shown on load) -->
        <ImageView
            android:id="@+id/image_view_thumbnail"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:contentDescription="@string/media_thumbnail" />

        <!-- Video play icon overlay -->
        <ImageView
            android:id="@+id/icon_play_overlay"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_play_circle"
            android:visibility="gone"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:contentDescription="@string/play_video" />

        <!-- Download button -->
        <ImageView
            android:id="@+id/button_download"
            android:layout_width="@dimen/download_button_size"
            android:layout_height="@dimen/download_button_size"
            android:layout_margin="@dimen/download_button_margin"
            android:src="@android:drawable/stat_sys_download"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="6dp"
            android:tint="@android:color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:contentDescription="@string/download_button" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>