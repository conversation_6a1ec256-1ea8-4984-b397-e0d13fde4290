<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.nativead.NativeAdView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_ad_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/card_background"
    android:layout_margin="8dp"
    android:padding="12dp"
    android:elevation="2dp">

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="120dp">

        <!-- App Icon -->
        <ImageView
            android:id="@+id/ad_app_icon"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="12dp"
            android:scaleType="centerCrop"
            android:background="@drawable/circle_background" />

        <!-- Content Section -->
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical">

            <!-- Headline -->
            <TextView
                android:id="@+id/ad_headline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginBottom="4dp" />

            <!-- Advertiser -->
            <TextView
                android:id="@+id/ad_advertiser"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="8dp"
                android:visibility="gone" />

            <!-- Body Text -->
            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="13sp"
                android:textColor="@color/text_primary"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginBottom="8dp"
                android:visibility="gone" />

            <!-- Call to Action Button -->
            <Button
                android:id="@+id/ad_call_to_action"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:background="@drawable/cta_button_background"
                android:paddingHorizontal="16dp"
                android:text="Install"
                android:minWidth="80dp" />

        </LinearLayout>

        <!-- Right Section -->
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp">

            <!-- Ad Badge -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ad"
                android:textSize="10sp"
                android:textColor="@color/white"
                android:background="@color/ad_badge_color"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:layout_gravity="end" />

            <!-- Star Rating (if available) -->
            <LinearLayout
                android:id="@+id/ad_stars_container"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="4dp"
                android:visibility="gone">

                <RatingBar
                    android:id="@+id/ad_stars"
                    style="?android:attr/ratingBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:isIndicator="true"
                    android:numStars="5"
                    android:stepSize="0.5" />

            </LinearLayout>

            <!-- Price (if available) -->
            <TextView
                android:id="@+id/ad_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/price_color"
                android:layout_gravity="end"
                android:layout_marginTop="4dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.gms.ads.nativead.NativeAdView>
