<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/shimmer_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="12dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFFFF"
        android:padding="16dp"
        android:elevation="4dp"
        android:minHeight="120dp">

        <!-- ✅ AD ATTRIBUTION SHIMMER -->
        <View
            android:id="@+id/shimmer_ad_attribution"
            android:layout_width="24dp"
            android:layout_height="16dp"
            android:background="@drawable/shimmer_rectangle"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:id="@+id/shimmer_content_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_below="@id/shimmer_ad_attribution"
            android:layout_marginTop="8dp"
            android:background="#F8F9FA"
            android:padding="12dp"
            android:minHeight="100dp">

        <!-- App Icon Shimmer -->
        <View
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/shimmer_circle"
            android:layout_marginEnd="12dp" />

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical">

            <!-- Headline Shimmer -->
            <View
                android:layout_width="match_parent"
                android:layout_height="16dp"
                android:background="@drawable/shimmer_rectangle"
                android:layout_marginBottom="8dp" />

            <!-- Second line of headline -->
            <View
                android:layout_width="200dp"
                android:layout_height="16dp"
                android:background="@drawable/shimmer_rectangle"
                android:layout_marginBottom="12dp" />

            <!-- CTA Button Shimmer -->
            <View
                android:layout_width="80dp"
                android:layout_height="32dp"
                android:background="@drawable/shimmer_button" />

        </LinearLayout>

        </LinearLayout>

    </RelativeLayout>

</com.facebook.shimmer.ShimmerFrameLayout>
