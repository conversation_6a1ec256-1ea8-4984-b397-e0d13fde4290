<?xml version="1.0" encoding="utf-8"?>
<!-- ✅ AUTO-SIZING NATIVE AD - NO FIXED DIMENSIONS -->
<com.google.android.gms.ads.nativead.NativeAdView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_ad_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:background="#FFFFFF"
    android:elevation="2dp"
    android:padding="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Top Row: Ad Attribution -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/ad_attribution"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="#E0E0E0"
                android:padding="4dp"
                android:text="Ad"
                android:textColor="#666666"
                android:textSize="10sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Main Content Row - AUTO SIZE -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- App Icon - AUTO SIZE -->
            <ImageView
                android:id="@+id/ad_app_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:contentDescription="App Icon"
                android:scaleType="fitCenter"
                android:adjustViewBounds="true"
                android:maxWidth="60dp"
                android:maxHeight="60dp" />

            <!-- Content Section - AUTO SIZE -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="12dp"
                android:orientation="vertical">

                <!-- Headline - AUTO SIZE -->
                <TextView
                    android:id="@+id/ad_headline"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="2dp" />

                <!-- Body Text - AUTO SIZE -->
                <TextView
                    android:id="@+id/ad_body"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:layout_marginBottom="2dp" />

                <!-- Advertiser - AUTO SIZE -->
                <TextView
                    android:id="@+id/ad_advertiser"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#888888"
                    android:textSize="12sp"
                    android:layout_marginBottom="2dp" />

                <!-- Star Rating - AUTO SIZE -->
                <RatingBar
                    android:id="@+id/ad_stars"
                    style="?android:attr/ratingBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:isIndicator="true"
                    android:numStars="5"
                    android:stepSize="0.5"
                    android:layout_marginBottom="2dp" />

                <!-- Price - AUTO SIZE -->
                <TextView
                    android:id="@+id/ad_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#1E88E5"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Call to Action Button - AUTO SIZE -->
            <Button
                android:id="@+id/ad_call_to_action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:backgroundTint="#1E88E5"
                android:text="Install"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:textStyle="bold"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.gms.ads.nativead.NativeAdView>
