package com.ks.app.service.statussaver.utils;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;

import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdLoader;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.nativead.NativeAd;
import com.google.android.gms.ads.nativead.NativeAdView;
import com.ks.app.service.statussaver.R;

/**
 * ✅ SIMPLE NATIVE AD MANAGER - Fresh Implementation
 * Clean, simple, and working native ad implementation
 */
public class SimpleNativeAdManager {
    
    private static final String TAG = "SimpleNativeAdManager";
    private static final String NATIVE_AD_UNIT_ID = "ca-app-pub-7557152164205920/5890575132";
    
    /**
     * ✅ Load Native Ad - Simple and Working
     * @param context Activity context
     * @param container FrameLayout to display the ad
     */
    public static void loadNativeAd(Context context, FrameLayout container) {
        if (context == null || container == null) {
            Log.w(TAG, "❌ Cannot load native ad - context or container is null");
            return;
        }

        Log.d(TAG, "🚀 Loading native ad...");
        Log.d(TAG, "📱 Ad Unit ID: " + NATIVE_AD_UNIT_ID);

        // Create AdLoader
        AdLoader adLoader = new AdLoader.Builder(context, NATIVE_AD_UNIT_ID)
                .forNativeAd(nativeAd -> {
                    Log.d(TAG, "✅ Native ad loaded successfully!");
                    displayNativeAd(context, container, nativeAd);
                })
                .withAdListener(new AdListener() {
                    @Override
                    public void onAdLoaded() {
                        Log.d(TAG, "📺 Ad loaded callback");
                    }

                    @Override
                    public void onAdFailedToLoad(LoadAdError adError) {
                        Log.w(TAG, "❌ Native ad failed to load");
                        Log.w(TAG, "Error: " + adError.getMessage());
                        Log.w(TAG, "Error code: " + adError.getCode());
                        
                        // Hide container on failure
                        container.setVisibility(View.GONE);
                    }

                    @Override
                    public void onAdClicked() {
                        Log.d(TAG, "👆 Native ad clicked");
                    }

                    @Override
                    public void onAdImpression() {
                        Log.d(TAG, "👁️ Native ad impression recorded");
                    }
                })
                .build();

        // Load the ad
        AdRequest adRequest = new AdRequest.Builder().build();
        adLoader.loadAd(adRequest);
    }

    /**
     * ✅ Display Native Ad - Populate and Show
     */
    private static void displayNativeAd(Context context, FrameLayout container, NativeAd nativeAd) {
        Log.d(TAG, "🎨 Displaying native ad...");

        // Inflate the native ad layout
        NativeAdView adView = (NativeAdView) LayoutInflater.from(context)
                .inflate(R.layout.native_ad_layout, null);

        // Populate the ad
        populateNativeAdView(nativeAd, adView);

        // Clear container and add the ad
        container.removeAllViews();
        container.addView(adView);
        container.setVisibility(View.VISIBLE);

        Log.d(TAG, "✅ Native ad displayed successfully!");
    }

    /**
     * ✅ Populate Native Ad View - No Hiding, Always Visible
     */
    private static void populateNativeAdView(NativeAd nativeAd, NativeAdView adView) {
        Log.d(TAG, "🔗 Populating native ad data - ensuring all content is visible...");

        // Set view references
        adView.setHeadlineView(adView.findViewById(R.id.ad_headline));
        adView.setBodyView(adView.findViewById(R.id.ad_body));
        adView.setCallToActionView(adView.findViewById(R.id.ad_call_to_action));
        adView.setIconView(adView.findViewById(R.id.ad_app_icon));
        adView.setStarRatingView(adView.findViewById(R.id.ad_stars));
        adView.setAdvertiserView(adView.findViewById(R.id.ad_advertiser));
        adView.setPriceView(adView.findViewById(R.id.ad_price));

        // ✅ ALWAYS MAKE ELEMENTS VISIBLE - NO HIDING

        // Populate headline (required) - ALWAYS VISIBLE
        if (nativeAd.getHeadline() != null && !nativeAd.getHeadline().isEmpty()) {
            ((TextView) adView.getHeadlineView()).setText(nativeAd.getHeadline());
        } else {
            ((TextView) adView.getHeadlineView()).setText("Download Now");
        }
        adView.getHeadlineView().setVisibility(View.VISIBLE);

        // Populate body - ALWAYS VISIBLE
        if (nativeAd.getBody() != null && !nativeAd.getBody().isEmpty()) {
            ((TextView) adView.getBodyView()).setText(nativeAd.getBody());
        } else {
            ((TextView) adView.getBodyView()).setText("Great app for your device");
        }
        adView.getBodyView().setVisibility(View.VISIBLE);

        // Populate call to action (required) - ALWAYS VISIBLE
        if (nativeAd.getCallToAction() != null && !nativeAd.getCallToAction().isEmpty()) {
            ((Button) adView.getCallToActionView()).setText(nativeAd.getCallToAction());
        } else {
            ((Button) adView.getCallToActionView()).setText("Install");
        }
        adView.getCallToActionView().setVisibility(View.VISIBLE);

        // Populate icon - ALWAYS VISIBLE
        if (nativeAd.getIcon() != null) {
            ((ImageView) adView.getIconView()).setImageDrawable(nativeAd.getIcon().getDrawable());
            Log.d(TAG, "✅ App icon loaded from ad data");
        } else {
            // Set a default background if no icon
            adView.getIconView().setBackgroundColor(0xFFE0E0E0);
            Log.d(TAG, "⚠️ No app icon in ad data - using default background");
        }
        adView.getIconView().setVisibility(View.VISIBLE);

        // Populate star rating - ALWAYS VISIBLE
        if (nativeAd.getStarRating() != null) {
            ((RatingBar) adView.getStarRatingView()).setRating(nativeAd.getStarRating().floatValue());
        } else {
            ((RatingBar) adView.getStarRatingView()).setRating(4.5f); // Default rating
        }
        adView.getStarRatingView().setVisibility(View.VISIBLE);

        // Populate advertiser - ALWAYS VISIBLE
        if (nativeAd.getAdvertiser() != null && !nativeAd.getAdvertiser().isEmpty()) {
            ((TextView) adView.getAdvertiserView()).setText(nativeAd.getAdvertiser());
        } else {
            ((TextView) adView.getAdvertiserView()).setText("Sponsored");
        }
        adView.getAdvertiserView().setVisibility(View.VISIBLE);

        // Populate price - ALWAYS VISIBLE
        if (nativeAd.getPrice() != null && !nativeAd.getPrice().isEmpty()) {
            ((TextView) adView.getPriceView()).setText(nativeAd.getPrice());
        } else {
            ((TextView) adView.getPriceView()).setText("Free");
        }
        adView.getPriceView().setVisibility(View.VISIBLE);

        // ✅ Ensure Ad Attribution is visible
        TextView attribution = adView.findViewById(R.id.ad_attribution);
        if (attribution != null) {
            attribution.setVisibility(View.VISIBLE);
        }

        // Register the NativeAdView with the NativeAd object
        adView.setNativeAd(nativeAd);

        Log.d(TAG, "✅ Native ad populated - ALL ELEMENTS VISIBLE, NO HIDING!");
    }

    /**
     * ✅ Destroy Native Ad - Cleanup
     */
    public static void destroyNativeAd(FrameLayout container) {
        if (container != null) {
            container.removeAllViews();
            container.setVisibility(View.GONE);
            Log.d(TAG, "🗑️ Native ad destroyed");
        }
    }
}
