package com.ks.app.service.statussaver.utils;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;
import com.facebook.shimmer.ShimmerFrameLayout;

import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdLoader;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.nativead.MediaView;
import com.google.android.gms.ads.nativead.NativeAd;
import com.google.android.gms.ads.nativead.NativeAdView;
import com.ks.app.service.statussaver.R;
import java.util.HashMap;
import java.util.Map;

/**
 * ✅ Native Ad Helper - Efficient AdMob Native Ad Management
 * Handles native ads for Home, Image Preview, and Video Preview screens
 * Following Google AdMob best practices for optimal performance and revenue
 */
public class NativeAdHelper {

    private static final String TAG = "NativeAdHelper";
    private static final String NATIVE_AD_UNIT_ID = "ca-app-pub-7557152164205920/5890575132";

    // ✅ Ad caching for efficiency
    private static final Map<String, NativeAd> adCache = new HashMap<>();
    private static final long AD_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
    private static final Map<String, Long> adCacheTimestamps = new HashMap<>();
    
    /**
     * ✅ Load Native Ad with Efficient Caching and Shimmer Loading
     * @param context Activity context
     * @param adContainer FrameLayout container for the ad
     * @param screenName Name of screen for logging (e.g., "Home", "ImagePreview")
     */
    public static void loadNativeAd(Context context, FrameLayout adContainer, String screenName) {
        if (context == null || adContainer == null) {
            Log.w(TAG, "❌ Cannot load native ad - context or container is null");
            return;
        }

        Log.d(TAG, "🔄 Loading native ad for " + screenName + " screen with efficient caching");
        Log.d(TAG, "📱 Native Ad Unit ID: " + NATIVE_AD_UNIT_ID);

        // ✅ Check cache first for efficiency
        NativeAd cachedAd = getCachedAd(screenName);
        if (cachedAd != null) {
            Log.d(TAG, "⚡ Using cached native ad for " + screenName + " - instant display");
            stopShimmerAndShowAd(context, adContainer, cachedAd, screenName);
            return;
        }

        // Show shimmer loading while ad loads
        showShimmerLoading(context, adContainer);

        // ✅ Preload next ad for better user experience
        preloadAdForNextScreen(context, screenName);

        AdLoader adLoader = new AdLoader.Builder(context, NATIVE_AD_UNIT_ID)
                .forNativeAd(nativeAd -> {
                    Log.d(TAG, "✅ Native ad loaded successfully for " + screenName);

                    // ✅ Cache the ad for efficiency
                    cacheAd(screenName, nativeAd);

                    // Stop shimmer and show actual ad
                    stopShimmerAndShowAd(context, adContainer, nativeAd, screenName);
                })
                .withAdListener(new AdListener() {
                    @Override
                    public void onAdLoaded() {
                        Log.d(TAG, "📺 Native ad loaded callback for " + screenName);
                    }

                    @Override
                    public void onAdFailedToLoad(LoadAdError adError) {
                        Log.w(TAG, "❌ Native ad failed to load for " + screenName);
                        Log.w(TAG, "📊 Error: " + adError.getMessage());
                        Log.w(TAG, "🔢 Error code: " + adError.getCode());
                        Log.w(TAG, "🌐 Error domain: " + adError.getDomain());

                        // Stop shimmer and hide container on failure
                        stopShimmerAndHide(adContainer);
                    }

                    @Override
                    public void onAdClicked() {
                        Log.d(TAG, "👆 Native ad clicked in " + screenName);
                        // ✅ Clear cache on click to get fresh ad next time
                        clearCachedAd(screenName);
                    }

                    @Override
                    public void onAdImpression() {
                        Log.d(TAG, "👁️ Native ad impression recorded for " + screenName);
                    }
                })
                .build();

        // ✅ Load with optimized AdRequest
        AdRequest adRequest = new AdRequest.Builder()
                .build();
        adLoader.loadAd(adRequest);
    }

    /**
     * ✅ Show Shimmer Loading Animation
     */
    private static void showShimmerLoading(Context context, FrameLayout adContainer) {
        Log.d(TAG, "✨ Starting shimmer loading animation");

        // Inflate shimmer layout
        View shimmerView = LayoutInflater.from(context)
                .inflate(R.layout.native_ad_shimmer, null);

        // Clear container and add shimmer
        adContainer.removeAllViews();
        adContainer.addView(shimmerView);

        // Start shimmer animation
        ShimmerFrameLayout shimmerContainer = shimmerView.findViewById(R.id.shimmer_container);
        if (shimmerContainer != null) {
            shimmerContainer.startShimmer();
        }
    }

    /**
     * ✅ Stop Shimmer and Show Actual Ad
     */
    private static void stopShimmerAndShowAd(Context context, FrameLayout adContainer,
                                           NativeAd nativeAd, String screenName) {
        Log.d(TAG, "🎯 Stopping shimmer and showing native ad for " + screenName);

        // Inflate compact native ad layout
        NativeAdView adView = (NativeAdView) LayoutInflater.from(context)
                .inflate(R.layout.native_ad_compact, null);

        // Populate the ad view
        populateNativeAdView(nativeAd, adView);

        // Replace shimmer with actual ad
        adContainer.removeAllViews();
        adContainer.addView(adView);

        Log.d(TAG, "✅ Native ad displayed in " + screenName + " screen");
    }

    /**
     * ✅ Stop Shimmer and Hide Container
     */
    private static void stopShimmerAndHide(FrameLayout adContainer) {
        Log.d(TAG, "❌ Stopping shimmer and hiding ad container");

        // Stop any running shimmer animation
        if (adContainer.getChildCount() > 0) {
            View child = adContainer.getChildAt(0);
            ShimmerFrameLayout shimmerContainer = child.findViewById(R.id.shimmer_container);
            if (shimmerContainer != null) {
                shimmerContainer.stopShimmer();
            }
        }

        // Hide the container
        adContainer.setVisibility(View.GONE);
    }
    
    /**
     * ✅ Populate Flexible Native Ad View with Ad Data
     * Maps ad data to UI components for flexible height layout (min 120dp)
     */
    private static void populateNativeAdView(NativeAd nativeAd, NativeAdView adView) {
        Log.d(TAG, "🎨 Populating flexible native ad view with ad data");

        // Set view references for compact layout
        adView.setHeadlineView(adView.findViewById(R.id.ad_headline));
        adView.setBodyView(adView.findViewById(R.id.ad_body));
        adView.setCallToActionView(adView.findViewById(R.id.ad_call_to_action));
        adView.setIconView(adView.findViewById(R.id.ad_app_icon));
        adView.setStarRatingView(adView.findViewById(R.id.ad_stars));
        adView.setAdvertiserView(adView.findViewById(R.id.ad_advertiser));
        adView.setPriceView(adView.findViewById(R.id.ad_price));

        // Populate headline (required) - flexible layout adapts to content
        if (nativeAd.getHeadline() != null) {
            ((TextView) adView.getHeadlineView()).setText(nativeAd.getHeadline());
        }

        // Populate body text (optional in flexible layout)
        if (nativeAd.getBody() != null && !nativeAd.getBody().isEmpty()) {
            adView.getBodyView().setVisibility(View.VISIBLE);
            ((TextView) adView.getBodyView()).setText(nativeAd.getBody());
        } else {
            adView.getBodyView().setVisibility(View.GONE);
        }

        // Populate call to action (required)
        if (nativeAd.getCallToAction() != null) {
            ((Button) adView.getCallToActionView()).setText(nativeAd.getCallToAction());
        }

        // Populate icon
        if (nativeAd.getIcon() != null) {
            ((ImageView) adView.getIconView()).setImageDrawable(nativeAd.getIcon().getDrawable());
            adView.getIconView().setVisibility(View.VISIBLE);
        } else {
            adView.getIconView().setVisibility(View.GONE);
        }

        // Populate star rating (flexible display)
        if (nativeAd.getStarRating() != null) {
            ((RatingBar) adView.getStarRatingView()).setRating(nativeAd.getStarRating().floatValue());
            adView.findViewById(R.id.ad_stars_container).setVisibility(View.VISIBLE);
        } else {
            adView.findViewById(R.id.ad_stars_container).setVisibility(View.GONE);
        }

        // Populate advertiser (optional in flexible layout)
        if (nativeAd.getAdvertiser() != null && !nativeAd.getAdvertiser().isEmpty()) {
            ((TextView) adView.getAdvertiserView()).setText(nativeAd.getAdvertiser());
            adView.getAdvertiserView().setVisibility(View.VISIBLE);
        } else {
            adView.getAdvertiserView().setVisibility(View.GONE);
        }

        // Populate price (optional in flexible layout)
        if (nativeAd.getPrice() != null && !nativeAd.getPrice().isEmpty()) {
            ((TextView) adView.getPriceView()).setText(nativeAd.getPrice());
            adView.getPriceView().setVisibility(View.VISIBLE);
        } else {
            adView.getPriceView().setVisibility(View.GONE);
        }

        // Register the NativeAdView with the NativeAd object
        adView.setNativeAd(nativeAd);

        Log.d(TAG, "✅ Flexible native ad view populated successfully");
    }
    
    /**
     * ✅ Destroy Native Ad with Shimmer Cleanup
     * Call this when the screen is destroyed to prevent memory leaks
     */
    public static void destroyNativeAd(FrameLayout adContainer) {
        if (adContainer != null) {
            // Stop any running shimmer animation
            if (adContainer.getChildCount() > 0) {
                View child = adContainer.getChildAt(0);
                ShimmerFrameLayout shimmerContainer = child.findViewById(R.id.shimmer_container);
                if (shimmerContainer != null) {
                    shimmerContainer.stopShimmer();
                    Log.d(TAG, "🛑 Shimmer animation stopped");
                }
            }

            adContainer.removeAllViews();
            Log.d(TAG, "🗑️ Native ad destroyed and container cleared");
        }
    }

    // ✅ ========== EFFICIENCY OPTIMIZATION METHODS ==========

    /**
     * ✅ Get Cached Ad - Check if we have a valid cached ad
     */
    private static NativeAd getCachedAd(String screenName) {
        if (!adCache.containsKey(screenName)) {
            return null;
        }

        Long timestamp = adCacheTimestamps.get(screenName);
        if (timestamp == null || (System.currentTimeMillis() - timestamp) > AD_CACHE_DURATION) {
            // Cache expired
            clearCachedAd(screenName);
            return null;
        }

        Log.d(TAG, "⚡ Found valid cached ad for " + screenName);
        return adCache.get(screenName);
    }

    /**
     * ✅ Cache Ad - Store ad for efficient reuse
     */
    private static void cacheAd(String screenName, NativeAd nativeAd) {
        adCache.put(screenName, nativeAd);
        adCacheTimestamps.put(screenName, System.currentTimeMillis());
        Log.d(TAG, "💾 Cached native ad for " + screenName + " for 5 minutes");
    }

    /**
     * ✅ Clear Cached Ad - Remove from cache
     */
    private static void clearCachedAd(String screenName) {
        NativeAd cachedAd = adCache.remove(screenName);
        adCacheTimestamps.remove(screenName);
        if (cachedAd != null) {
            cachedAd.destroy();
            Log.d(TAG, "🗑️ Cleared cached ad for " + screenName);
        }
    }

    /**
     * ✅ Preload Ad for Next Screen - Improve user experience
     */
    private static void preloadAdForNextScreen(Context context, String currentScreen) {
        // Preload strategy based on user flow
        String nextScreen = getNextLikelyScreen(currentScreen);
        if (nextScreen != null && !adCache.containsKey(nextScreen)) {
            Log.d(TAG, "🚀 Preloading ad for likely next screen: " + nextScreen);

            AdLoader preloader = new AdLoader.Builder(context, NATIVE_AD_UNIT_ID)
                    .forNativeAd(nativeAd -> {
                        cacheAd(nextScreen, nativeAd);
                        Log.d(TAG, "✅ Preloaded ad for " + nextScreen);
                    })
                    .withAdListener(new AdListener() {
                        @Override
                        public void onAdFailedToLoad(LoadAdError adError) {
                            Log.d(TAG, "⚠️ Preload failed for " + nextScreen + ": " + adError.getMessage());
                        }
                    })
                    .build();

            preloader.loadAd(new AdRequest.Builder().build());
        }
    }

    /**
     * ✅ Get Next Likely Screen - Predict user navigation
     */
    private static String getNextLikelyScreen(String currentScreen) {
        switch (currentScreen) {
            case "Home":
                return "ImagePreview"; // Users often go to image preview from home
            case "ImagePreview":
                return "VideoPreview"; // Users might switch to video preview
            case "VideoPreview":
                return "Home"; // Users might go back to home
            default:
                return null;
        }
    }

    /**
     * ✅ Clear All Cached Ads - Call when app is destroyed
     */
    public static void clearAllCachedAds() {
        for (NativeAd ad : adCache.values()) {
            if (ad != null) {
                ad.destroy();
            }
        }
        adCache.clear();
        adCacheTimestamps.clear();
        Log.d(TAG, "🧹 All cached native ads cleared");
    }
}
