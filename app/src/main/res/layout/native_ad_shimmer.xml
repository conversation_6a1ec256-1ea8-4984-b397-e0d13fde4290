<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/shimmer_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#FFFFFF"
        android:padding="8dp">

        <!-- ✅ AD ATTRIBUTION SHIMMER -->
        <View
            android:id="@+id/shimmer_ad_attribution"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shimmer_rectangle"
            android:layout_gravity="end" />

        <LinearLayout
            android:id="@+id/shimmer_content_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="#F8F9FA"
            android:padding="8dp">

        <!-- App Icon Shimmer -->
        <View
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@drawable/shimmer_circle"
            android:layout_marginEnd="8dp" />

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical">

            <!-- Headline Shimmer -->
            <View
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shimmer_rectangle" />

            <!-- Second line of headline -->
            <View
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shimmer_rectangle" />

            <!-- CTA Button Shimmer -->
            <View
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shimmer_button" />

        </LinearLayout>

        </LinearLayout>

    </RelativeLayout>

</com.facebook.shimmer.ShimmerFrameLayout>
