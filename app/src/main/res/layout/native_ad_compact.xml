<?xml version="1.0" encoding="utf-8"?>
<!-- ✅ FIXED: Non-Overlapping Native Ad Layout -->
<com.google.android.gms.ads.nativead.NativeAdView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_ad_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="120dp"
    android:layout_margin="8dp"
    android:background="@drawable/ad_background"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- ✅ AD ATTRIBUTION - Top Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/ad_attribution"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ad"
                android:textColor="#666666"
                android:background="#E0E0E0"
                android:padding="4dp"
                android:textSize="10sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- ✅ Main Content Row - No Overlapping -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- App Icon -->
            <ImageView
                android:id="@+id/ad_app_icon"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginEnd="12dp"
                android:scaleType="centerCrop"
                android:contentDescription="Ad App Icon" />

            <!-- Content Section -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="12dp"
                android:orientation="vertical">

                <!-- Headline -->
                <TextView
                    android:id="@+id/ad_headline"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#000000"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:layout_marginBottom="2dp" />

                <!-- Body Text -->
                <TextView
                    android:id="@+id/ad_body"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#666666"
                    android:textSize="12sp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:layout_marginBottom="2dp"
                    android:visibility="gone" />

                <!-- Advertiser -->
                <TextView
                    android:id="@+id/ad_advertiser"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#888888"
                    android:textSize="11sp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="gone" />

                <!-- Star Rating -->
                <RatingBar
                    android:id="@+id/ad_stars"
                    style="?android:attr/ratingBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:isIndicator="true"
                    android:numStars="5"
                    android:stepSize="0.5"
                    android:visibility="gone" />

                <!-- Price -->
                <TextView
                    android:id="@+id/ad_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#1E88E5"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:layout_marginTop="2dp"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- Call to Action Button -->
            <Button
                android:id="@+id/ad_call_to_action"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:textColor="#FFFFFF"
                android:backgroundTint="#1E88E5"
                android:textSize="12sp"
                android:textStyle="bold"
                android:minWidth="70dp"
                android:paddingLeft="12dp"
                android:paddingRight="12dp"
                android:text="Install" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.gms.ads.nativead.NativeAdView>
