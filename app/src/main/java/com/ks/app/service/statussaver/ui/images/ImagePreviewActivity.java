package com.ks.app.service.statussaver.ui.images;

import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.FrameLayout;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.bumptech.glide.Glide;
import com.github.chrisbanes.photoview.PhotoView;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.utils.FileUtils;
import com.ks.app.service.statussaver.utils.SimpleNativeAdManager;

public class ImagePreviewActivity extends AppCompatActivity {

    private Uri mediaUri;
    private String fileName;
    private boolean showDownloadButton;
    private FrameLayout nativeAdContainer;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_image_preview);

        PhotoView photoView = findViewById(R.id.photo_view_preview);
        Button buttonDownload = findViewById(R.id.button_download_preview);
        Button buttonShare = findViewById(R.id.button_share_preview);
        ImageButton buttonBack = findViewById(R.id.button_back);

        if (getIntent().getExtras() != null) {
            String uriString = getIntent().getStringExtra("media_uri");
            if (uriString != null) {
                mediaUri = Uri.parse(uriString);
            }
            fileName = getIntent().getStringExtra("file_name");
            // boolean isVideo = getIntent().getBooleanExtra("is_video", false); // Not directly used for image loading
            showDownloadButton = getIntent().getBooleanExtra("show_download_button", true);
        }

        // Setup back button
        buttonBack.setOnClickListener(v -> finish());

        if (mediaUri != null) {
            Glide.with(this)
                    .load(mediaUri)
                    .fitCenter()
                    .into(photoView);
        } else {
            Toast.makeText(this, "Error loading image.", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        if (showDownloadButton) {
            buttonDownload.setVisibility(View.VISIBLE);
            buttonDownload.setOnClickListener(v -> downloadMedia());
        } else {
            buttonDownload.setVisibility(View.GONE);
        }

        buttonShare.setOnClickListener(v -> shareMedia());

        // Setup native ad
        setupNativeAd();

        // Optional: Set up system UI for immersive mode (hide status/navigation bars)
        getWindow().getDecorView().setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION // hide nav bar
            | View.SYSTEM_UI_FLAG_FULLSCREEN // hide status bar
            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
    }

    private void downloadMedia() {
        if (mediaUri != null && fileName != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                FileUtils.saveStatus(this, mediaUri, fileName, false); // false for isVideo
            } else {
                FileUtils.saveStatusLegacy(this, mediaUri, fileName, false);
            }
        } else {
            Toast.makeText(this, "Cannot download file.", Toast.LENGTH_SHORT).show();
        }
    }

    private void shareMedia() {
        if (mediaUri != null) {
            FileUtils.shareMedia(this, mediaUri);
        } else {
            Toast.makeText(this, "Cannot share file.", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * ✅ Setup Native Ad for Image Preview
     */
    private void setupNativeAd() {
        nativeAdContainer = findViewById(R.id.native_ad_container);

        if (nativeAdContainer != null) {
            // Load native ad using simple manager
            SimpleNativeAdManager.loadNativeAd(this, nativeAdContainer);
        }
    }

    @Override
    protected void onDestroy() {
        // Destroy native ad to prevent memory leaks
        if (nativeAdContainer != null) {
            SimpleNativeAdManager.destroyNativeAd(nativeAdContainer);
        }
        super.onDestroy();
    }
}