package com.ks.app.service.statussaver.ui.videos;

import android.app.ActivityManager;
import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;

import android.widget.FrameLayout;
import android.widget.Toast;
import android.widget.ImageButton;
import android.widget.SeekBar;
import android.os.Handler;
import android.os.Looper;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.ui.StyledPlayerView;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.utils.ExoPlayerConfig;
import com.ks.app.service.statussaver.utils.FileUtils;


public class VideoPreviewActivity extends AppCompatActivity {

    private static final String TAG = "VideoPreviewActivity";

    private Uri mediaUri;
    private String fileName;
    private boolean showDownloadButton;
    private ExoPlayer exoPlayer;
    private StyledPlayerView playerView;
    private ImageButton buttonPlayPause;
    private SeekBar videoProgressBar;
    private Handler progressHandler;
    private boolean isPlaying = false;

    private boolean isLowEndDevice = false;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_video_preview);

        // ✅ Memory Optimization: Detect low-end devices for optimized video handling
        detectDeviceMemoryClass();

        // Initialize ExoPlayer components
        playerView = findViewById(R.id.video_view_preview);
        buttonPlayPause = findViewById(R.id.button_play_pause);
        videoProgressBar = findViewById(R.id.video_progress_bar);
        progressHandler = new Handler(Looper.getMainLooper());
        Button buttonDownload = findViewById(R.id.button_download_preview_video);
        Button buttonShare = findViewById(R.id.button_share_preview_video);
        ImageButton buttonBack = findViewById(R.id.button_back);

        if (getIntent().getExtras() != null) {
            String uriString = getIntent().getStringExtra("media_uri");
            if (uriString != null) {
                mediaUri = Uri.parse(uriString);
            }
            fileName = getIntent().getStringExtra("file_name");
            showDownloadButton = getIntent().getBooleanExtra("show_download_button", true);
        }

        // Setup back button
        buttonBack.setOnClickListener(v -> finish());

        if (mediaUri != null) {
            setupVideoPlayer();
        } else {
            Toast.makeText(this, "Error loading video.", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        if (showDownloadButton) {
            buttonDownload.setVisibility(View.VISIBLE);
            buttonDownload.setOnClickListener(v -> downloadMedia());
        } else {
            buttonDownload.setVisibility(View.GONE);
        }

        buttonShare.setOnClickListener(v -> shareMedia());



        // Optional: Set up system UI for immersive mode
        getWindow().getDecorView().setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION // hide nav bar
            | View.SYSTEM_UI_FLAG_FULLSCREEN // hide status bar
            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
    }

    private void setupVideoPlayer() {
        // ✅ Create Optimized ExoPlayer with Memory Management
        exoPlayer = ExoPlayerConfig.createOptimizedPlayer(this, isLowEndDevice);

        // Attach player to PlayerView
        playerView.setPlayer(exoPlayer);

        // ✅ Configure ExoPlayer for Memory Efficiency
        playerView.setUseController(true);
        playerView.setControllerAutoShow(false);

        // Create MediaItem from URI
        MediaItem mediaItem = MediaItem.fromUri(mediaUri);
        exoPlayer.setMediaItem(mediaItem);

        // ✅ Setup Player Event Listener for Memory Management
        exoPlayer.addListener(new Player.Listener() {
            @Override
            public void onPlaybackStateChanged(int playbackState) {
                switch (playbackState) {
                    case Player.STATE_READY:
                        Log.d(TAG, "ExoPlayer ready - video prepared successfully");

                        // ✅ Memory Monitoring
                        if (ExoPlayerConfig.MemoryMonitor.isMemoryUsageCritical()) {
                            Log.w(TAG, "Critical memory usage detected during video playback");
                        }
                        break;

                    case Player.STATE_ENDED:
                        Log.d(TAG, "Video playback ended");
                        break;

                    case Player.STATE_BUFFERING:
                        Log.d(TAG, "Video buffering...");
                        break;
                }
            }

            @Override
            public void onIsPlayingChanged(boolean isPlaying) {
                VideoPreviewActivity.this.isPlaying = isPlaying;
                updatePlayPauseButton();
            }
        });

        // Prepare the player
        exoPlayer.prepare();

        // ✅ DO NOT auto-play - wait for user interaction
        exoPlayer.setPlayWhenReady(false);

        // Setup custom controls
        setupCustomControls();

        Log.d(TAG, "ExoPlayer setup completed with custom controls");
    }





    private void downloadMedia() {
        if (mediaUri != null && fileName != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                FileUtils.saveStatus(this, mediaUri, fileName, true); // true for isVideo
            } else {
                FileUtils.saveStatusLegacy(this, mediaUri, fileName, true);
            }
        } else {
            Toast.makeText(this, "Cannot download file.", Toast.LENGTH_SHORT).show();
        }
    }

    private void shareMedia() {
        if (mediaUri != null) {
            // Pause ExoPlayer before sharing if it's playing
            ExoPlayerConfig.LifecycleManager.pausePlayer(exoPlayer);
            FileUtils.shareMedia(this, mediaUri);
        } else {
            Toast.makeText(this, "Cannot share file.", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        // ✅ ExoPlayer Memory Optimization: Pause player on activity pause
        ExoPlayerConfig.LifecycleManager.pausePlayer(exoPlayer);

        // ✅ Low-end device optimization: Release video resources immediately on pause
        if (isLowEndDevice) {
            Log.d(TAG, "Low-end device: Releasing ExoPlayer resources on pause");
            releaseVideoPlayer();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        // ✅ ExoPlayer Best Practice: Release player resources in onStop()
        releaseVideoPlayer();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Video will auto-play when ready
    }

    @Override
    protected void onDestroy() {

        // ✅ Clean up progress handler
        if (progressHandler != null) {
            progressHandler.removeCallbacks(updateProgressRunnable);
        }

        super.onDestroy();
        // ✅ Best Practice 2025: Ensure video memory is released
        releaseVideoPlayer();
    }

    /**
     * ✅ Memory Optimization: Detect device memory class for optimized video handling
     */
    private void detectDeviceMemoryClass() {
        try {
            ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager != null) {
                int memoryClass = activityManager.getMemoryClass(); // in MB
                isLowEndDevice = memoryClass <= 128; // Consider devices with 128MB or less as low-end

                Log.d(TAG, "Device memory class: " + memoryClass + "MB, Low-end device: " + isLowEndDevice);

                if (isLowEndDevice) {
                    Log.d(TAG, "Low-end device detected: Enabling memory optimizations");
                }
            }
        } catch (Exception e) {
            Log.w(TAG, "Error detecting device memory class: " + e.getMessage());
            isLowEndDevice = false; // Default to false if detection fails
        }
    }

    /**
     * ✅ Enhanced ExoPlayer Memory Management
     * Releases ExoPlayer resources to prevent memory leaks and OOM errors
     */
    private void releaseVideoPlayer() {
        try {
            // ✅ Use ExoPlayerConfig lifecycle management
            ExoPlayerConfig.LifecycleManager.releasePlayer(exoPlayer);
            exoPlayer = null;

            // UI state reset - no play button to show

            // ✅ Memory monitoring and cleanup
            long memoryUsage = ExoPlayerConfig.MemoryMonitor.getCurrentMemoryUsage();
            Log.d(TAG, "ExoPlayer released. Current memory usage: " + memoryUsage + "MB");

            // ✅ Low-end device optimization: Force garbage collection
            if (isLowEndDevice) {
                System.gc();
                Log.d(TAG, "Low-end device: Forced garbage collection after ExoPlayer release");
            }

        } catch (Exception e) {
            Log.w(TAG, "Error releasing ExoPlayer: " + e.getMessage());
        }
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        // ✅ Memory Optimization: Release video resources on memory pressure
        if (level >= TRIM_MEMORY_MODERATE) {
            Log.w(TAG, "Memory trim requested (level: " + level + ") - releasing video player");
            releaseVideoPlayer();
        }
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        // ✅ Enhanced Memory Management: Release video memory on low memory warning
        Log.w(TAG, "Low memory warning - releasing video player");
        releaseVideoPlayer();
    }

    /**
     * ✅ Setup Custom Play/Pause and Progress Controls
     */
    private void setupCustomControls() {
        // Play/Pause button click listener
        buttonPlayPause.setOnClickListener(v -> {
            if (exoPlayer != null) {
                if (isPlaying) {
                    exoPlayer.pause();
                } else {
                    exoPlayer.play();
                }
            }
        });

        // Progress bar seek listener
        videoProgressBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser && exoPlayer != null) {
                    long duration = exoPlayer.getDuration();
                    if (duration > 0) {
                        long seekPosition = (duration * progress) / 100;
                        exoPlayer.seekTo(seekPosition);
                    }
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                // Stop progress updates while user is dragging
                progressHandler.removeCallbacks(updateProgressRunnable);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                // Resume progress updates
                updateProgress();
            }
        });

        // Start progress updates
        updateProgress();
    }

    /**
     * ✅ Update Play/Pause Button Icon
     */
    private void updatePlayPauseButton() {
        if (buttonPlayPause != null) {
            if (isPlaying) {
                buttonPlayPause.setBackgroundResource(R.drawable.ic_pause_circle);
            } else {
                buttonPlayPause.setBackgroundResource(R.drawable.ic_play_circle);
            }
        }
    }

    /**
     * ✅ Update Progress Bar
     */
    private void updateProgress() {
        if (exoPlayer != null && videoProgressBar != null) {
            long duration = exoPlayer.getDuration();
            long currentPosition = exoPlayer.getCurrentPosition();

            if (duration > 0) {
                int progress = (int) ((currentPosition * 100) / duration);
                videoProgressBar.setProgress(progress);
            }
        }

        // Schedule next update
        progressHandler.postDelayed(updateProgressRunnable, 1000);
    }

    /**
     * ✅ Progress Update Runnable
     */
    private final Runnable updateProgressRunnable = new Runnable() {
        @Override
        public void run() {
            updateProgress();
        }
    };

}