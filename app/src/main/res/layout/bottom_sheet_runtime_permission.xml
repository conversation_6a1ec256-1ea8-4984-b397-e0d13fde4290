<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center_horizontal"
    android:background="@drawable/permission_dialog_background"
    app:behavior_hideable="true"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <!-- Title -->
    <TextView
        android:id="@+id/text_view_runtime_permission_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/permission_needed_title"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
        android:textColor="@android:color/white"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Message -->
    <TextView
        android:id="@+id/text_view_runtime_permission_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/permission_denied_message"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
        android:textColor="@android:color/white"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Primary Button -->
    <Button
        android:id="@+id/button_settings_runtime"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/open_settings"
        android:textColor="@android:color/white"
        style="@style/Widget.MaterialComponents.Button" />

    <!-- Secondary Button -->
    <Button
        android:id="@+id/button_cancel_runtime"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/not_now"
        android:textColor="@android:color/white"
        android:layout_marginTop="12dp"
        style="@style/Widget.MaterialComponents.Button.TextButton" />

</LinearLayout>