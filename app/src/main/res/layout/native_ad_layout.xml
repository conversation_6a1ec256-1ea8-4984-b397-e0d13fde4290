<?xml version="1.0" encoding="utf-8"?>
<!-- ✅ FIXED NATIVE AD LAYOUT - No Hiding, No Resizing -->
<com.google.android.gms.ads.nativead.NativeAdView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_ad_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:background="#FFFFFF"
    android:elevation="2dp"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:minHeight="120dp">

        <!-- Top Row: Ad Attribution -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/ad_attribution"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="#E0E0E0"
                android:padding="4dp"
                android:text="Ad"
                android:textColor="#666666"
                android:textSize="10sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Main Content Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="top">

            <!-- App Icon - Fixed Size, No Resizing -->
            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp">

                <ImageView
                    android:id="@+id/ad_app_icon"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:contentDescription="App Icon"
                    android:scaleType="fitCenter"
                    android:background="#F0F0F0" />

            </FrameLayout>

            <!-- Content Section - No Width Constraints -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="16dp"
                android:orientation="vertical">

                <!-- Headline - Full Width -->
                <TextView
                    android:id="@+id/ad_headline"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp"
                    android:text="App Headline Here" />

                <!-- Body Text - Full Width -->
                <TextView
                    android:id="@+id/ad_body"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp"
                    android:visibility="visible"
                    android:text="App description text here" />

                <!-- Advertiser -->
                <TextView
                    android:id="@+id/ad_advertiser"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#888888"
                    android:textSize="12sp"
                    android:layout_marginBottom="4dp"
                    android:visibility="visible"
                    android:text="Advertiser Name" />

                <!-- Star Rating -->
                <RatingBar
                    android:id="@+id/ad_stars"
                    style="?android:attr/ratingBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:isIndicator="true"
                    android:numStars="5"
                    android:stepSize="0.5"
                    android:rating="4.5"
                    android:layout_marginBottom="4dp"
                    android:visibility="visible" />

                <!-- Price -->
                <TextView
                    android:id="@+id/ad_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#1E88E5"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:visibility="visible"
                    android:text="Free" />

            </LinearLayout>

            <!-- Call to Action Button - Fixed Size -->
            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <Button
                    android:id="@+id/ad_call_to_action"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:backgroundTint="#1E88E5"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:text="Install"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:minWidth="80dp" />

            </FrameLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.gms.ads.nativead.NativeAdView>
