package com.ks.app.service.statussaver.utils;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;

import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdLoader;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.nativead.NativeAd;
import com.google.android.gms.ads.nativead.NativeAdView;
import com.ks.app.service.statussaver.R;

/**
 * ✅ SIMPLE NATIVE AD MANAGER - Fresh Implementation
 * Clean, simple, and working native ad implementation
 */
public class SimpleNativeAdManager {
    
    private static final String TAG = "SimpleNativeAdManager";
    private static final String NATIVE_AD_UNIT_ID = "ca-app-pub-7557152164205920/5890575132";
    
    /**
     * ✅ Load Native Ad - Simple and Working
     * @param context Activity context
     * @param container FrameLayout to display the ad
     */
    public static void loadNativeAd(Context context, FrameLayout container) {
        if (context == null || container == null) {
            Log.w(TAG, "❌ Cannot load native ad - context or container is null");
            return;
        }

        Log.d(TAG, "🚀 Loading native ad...");
        Log.d(TAG, "📱 Ad Unit ID: " + NATIVE_AD_UNIT_ID);

        // Create AdLoader
        AdLoader adLoader = new AdLoader.Builder(context, NATIVE_AD_UNIT_ID)
                .forNativeAd(nativeAd -> {
                    Log.d(TAG, "✅ Native ad loaded successfully!");
                    displayNativeAd(context, container, nativeAd);
                })
                .withAdListener(new AdListener() {
                    @Override
                    public void onAdLoaded() {
                        Log.d(TAG, "📺 Ad loaded callback");
                    }

                    @Override
                    public void onAdFailedToLoad(LoadAdError adError) {
                        Log.w(TAG, "❌ Native ad failed to load");
                        Log.w(TAG, "Error: " + adError.getMessage());
                        Log.w(TAG, "Error code: " + adError.getCode());
                        
                        // Hide container on failure
                        container.setVisibility(View.GONE);
                    }

                    @Override
                    public void onAdClicked() {
                        Log.d(TAG, "👆 Native ad clicked");
                    }

                    @Override
                    public void onAdImpression() {
                        Log.d(TAG, "👁️ Native ad impression recorded");
                    }
                })
                .build();

        // Load the ad
        AdRequest adRequest = new AdRequest.Builder().build();
        adLoader.loadAd(adRequest);
    }

    /**
     * ✅ Display Native Ad - AUTO-SIZING, NO RESIZING
     */
    private static void displayNativeAd(Context context, FrameLayout container, NativeAd nativeAd) {
        Log.d(TAG, "🎨 Displaying auto-sizing native ad...");

        // ✅ Step 1: Inflate the native ad layout
        NativeAdView adView = (NativeAdView) LayoutInflater.from(context)
                .inflate(R.layout.native_ad_layout, null);

        // ✅ Step 2: Populate with natural content sizing
        populateNativeAdView(nativeAd, adView);

        // ✅ Step 3: Clear container and add the ad AFTER loading
        container.removeAllViews();
        container.addView(adView);
        container.setVisibility(View.VISIBLE);

        // ✅ Step 4: Bring to front to prevent overlap issues
        container.bringToFront();

        Log.d(TAG, "✅ Auto-sizing native ad displayed - content determines size naturally!");
    }

    /**
     * ✅ Populate Native Ad View - AUTO-SIZING CONTENT
     */
    private static void populateNativeAdView(NativeAd nativeAd, NativeAdView adView) {
        Log.d(TAG, "🔗 AUTO-SIZING: Populating native ad with natural content sizing...");

        // ✅ STEP 1: REQUIRED BINDING - HEADLINE
        TextView headlineView = adView.findViewById(R.id.ad_headline);
        adView.setHeadlineView(headlineView);
        if (nativeAd.getHeadline() != null && !nativeAd.getHeadline().isEmpty()) {
            headlineView.setText(nativeAd.getHeadline());
            headlineView.setVisibility(View.VISIBLE);
        } else {
            headlineView.setVisibility(View.GONE);
        }
        Log.d(TAG, "✅ Headline: " + (headlineView.getVisibility() == View.VISIBLE ? headlineView.getText() : "hidden"));

        // ✅ STEP 2: REQUIRED BINDING - CALL TO ACTION
        Button ctaView = adView.findViewById(R.id.ad_call_to_action);
        adView.setCallToActionView(ctaView);
        if (nativeAd.getCallToAction() != null && !nativeAd.getCallToAction().isEmpty()) {
            ctaView.setText(nativeAd.getCallToAction());
            ctaView.setVisibility(View.VISIBLE);
        } else {
            ctaView.setText("Install");
            ctaView.setVisibility(View.VISIBLE);
        }
        Log.d(TAG, "✅ CTA: " + ctaView.getText());

        // ✅ STEP 3: OPTIONAL CONTENT - NATURAL SIZING

        // ✅ BODY TEXT - SHOW IF AVAILABLE
        TextView bodyView = adView.findViewById(R.id.ad_body);
        if (nativeAd.getBody() != null && !nativeAd.getBody().isEmpty()) {
            adView.setBodyView(bodyView);
            bodyView.setText(nativeAd.getBody());
            bodyView.setVisibility(View.VISIBLE);
            Log.d(TAG, "✅ Body: " + nativeAd.getBody());
        } else {
            bodyView.setVisibility(View.GONE);
            Log.d(TAG, "⚠️ No body - hidden");
        }

        // ✅ APP ICON - SHOW IF AVAILABLE
        ImageView iconView = adView.findViewById(R.id.ad_app_icon);
        if (nativeAd.getIcon() != null) {
            adView.setIconView(iconView);
            iconView.setImageDrawable(nativeAd.getIcon().getDrawable());
            iconView.setVisibility(View.VISIBLE);
            Log.d(TAG, "✅ Icon: loaded from ad");
        } else {
            iconView.setVisibility(View.GONE);
            Log.d(TAG, "⚠️ No icon - hidden");
        }

        // ✅ STAR RATING - SHOW IF AVAILABLE
        RatingBar starsView = adView.findViewById(R.id.ad_stars);
        if (nativeAd.getStarRating() != null) {
            adView.setStarRatingView(starsView);
            starsView.setRating(nativeAd.getStarRating().floatValue());
            starsView.setVisibility(View.VISIBLE);
            Log.d(TAG, "✅ Stars: " + nativeAd.getStarRating());
        } else {
            starsView.setVisibility(View.GONE);
            Log.d(TAG, "⚠️ No stars - hidden");
        }

        // ✅ ADVERTISER - SHOW IF AVAILABLE
        TextView advertiserView = adView.findViewById(R.id.ad_advertiser);
        if (nativeAd.getAdvertiser() != null && !nativeAd.getAdvertiser().isEmpty()) {
            adView.setAdvertiserView(advertiserView);
            advertiserView.setText(nativeAd.getAdvertiser());
            advertiserView.setVisibility(View.VISIBLE);
            Log.d(TAG, "✅ Advertiser: " + nativeAd.getAdvertiser());
        } else {
            advertiserView.setVisibility(View.GONE);
            Log.d(TAG, "⚠️ No advertiser - hidden");
        }

        // ✅ PRICE - SHOW IF AVAILABLE
        TextView priceView = adView.findViewById(R.id.ad_price);
        if (nativeAd.getPrice() != null && !nativeAd.getPrice().isEmpty()) {
            adView.setPriceView(priceView);
            priceView.setText(nativeAd.getPrice());
            priceView.setVisibility(View.VISIBLE);
            Log.d(TAG, "✅ Price: " + nativeAd.getPrice());
        } else {
            priceView.setVisibility(View.GONE);
            Log.d(TAG, "⚠️ No price - hidden");
        }

        // ✅ AD ATTRIBUTION - ALWAYS VISIBLE
        TextView attribution = adView.findViewById(R.id.ad_attribution);
        if (attribution != null) {
            attribution.setVisibility(View.VISIBLE);
        }

        // ✅ FINAL STEP: Register the NativeAdView with the NativeAd object
        adView.setNativeAd(nativeAd);

        Log.d(TAG, "✅ AUTO-SIZING COMPLETE - Content shows naturally without resizing!");
    }

    /**
     * ✅ Destroy Native Ad - Cleanup
     */
    public static void destroyNativeAd(FrameLayout container) {
        if (container != null) {
            container.removeAllViews();
            container.setVisibility(View.GONE);
            Log.d(TAG, "🗑️ Native ad destroyed");
        }
    }
}
