package com.ks.app.service.statussaver.ui.adapters;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.data.models.MediaItem;
import com.ks.app.service.statussaver.utils.PerformanceConfig;
import java.util.ArrayList;
import java.util.List;

/**
 * High-performance adapter for media items with DiffUtil optimization
 * Designed to handle large datasets efficiently without Paging 3
 */
public class OptimizedMediaAdapter extends RecyclerView.Adapter<OptimizedMediaAdapter.MediaViewHolder> {
    private static final String TAG = "OptimizedMediaAdapter";
    
    private final Context context;
    private final List<MediaItem> mediaItems;
    private final OnMediaItemClickListener clickListener;
    private final OnDownloadClickListener downloadListener;
    private final boolean showDownloadButton;
    
    public interface OnMediaItemClickListener {
        void onMediaItemClick(MediaItem mediaItem);
    }
    
    public interface OnDownloadClickListener {
        void onDownloadClick(MediaItem mediaItem);
    }
    
    public OptimizedMediaAdapter(Context context, 
                               OnMediaItemClickListener clickListener,
                               OnDownloadClickListener downloadListener,
                               boolean showDownloadButton) {
        this.context = context;
        this.mediaItems = new ArrayList<>();
        this.clickListener = clickListener;
        this.downloadListener = downloadListener;
        this.showDownloadButton = showDownloadButton;
    }
    
    @NonNull
    @Override
    public MediaViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_media, parent, false);
        return new MediaViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull MediaViewHolder holder, int position) {
        MediaItem mediaItem = mediaItems.get(position);
        holder.bind(mediaItem);
    }
    
    @Override
    public int getItemCount() {
        return mediaItems.size();
    }
    
    /**
     * Update items using DiffUtil for efficient updates
     */
    public void updateItems(List<MediaItem> newItems) {
        if (newItems == null) {
            newItems = new ArrayList<>();
        }
        
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new MediaDiffCallback(mediaItems, newItems));
        
        mediaItems.clear();
        mediaItems.addAll(newItems);
        
        diffResult.dispatchUpdatesTo(this);
    }
    
    /**
     * Clear all items
     */
    public void clearItems() {
        int size = mediaItems.size();
        mediaItems.clear();
        notifyItemRangeRemoved(0, size);
    }
    
    /**
     * Check if adapter is empty
     */
    public boolean isEmpty() {
        return mediaItems.isEmpty();
    }
    
    class MediaViewHolder extends RecyclerView.ViewHolder {
        private final ImageView imageViewThumbnail;
        private final ImageView buttonDownload;
        private final View placeholderBackground;
        private final ImageView iconMediaType;
        private final ImageView iconPlayOverlay;

        MediaViewHolder(@NonNull View itemView) {
            super(itemView);
            imageViewThumbnail = itemView.findViewById(R.id.image_view_thumbnail);
            buttonDownload = itemView.findViewById(R.id.button_download);
            placeholderBackground = itemView.findViewById(R.id.placeholder_background);
            iconMediaType = itemView.findViewById(R.id.icon_media_type);
            iconPlayOverlay = itemView.findViewById(R.id.icon_play_overlay);
        }
        
        void bind(MediaItem mediaItem) {
            // Show appropriate media type icon and play overlay
            if (mediaItem.isVideo()) {
                iconMediaType.setImageResource(android.R.drawable.ic_media_play);
                iconPlayOverlay.setVisibility(View.VISIBLE);
            } else {
                iconMediaType.setImageResource(R.drawable.ic_image_placeholder);
                iconPlayOverlay.setVisibility(View.GONE);
            }

            // Initially show placeholder
            placeholderBackground.setVisibility(View.VISIBLE);
            iconMediaType.setVisibility(View.VISIBLE);
            imageViewThumbnail.setVisibility(View.GONE);

            // Load thumbnail with optimized Glide settings
            if (mediaItem.isVideo()) {
                // For videos, try to load video frame
                Glide.with(context)
                        .asBitmap()
                        .load(mediaItem.getUri())
                        .apply(PerformanceConfig.getVideoThumbnailOptions())
                        .into(new com.bumptech.glide.request.target.ImageViewTarget<android.graphics.Bitmap>(imageViewThumbnail) {
                            @Override
                            protected void setResource(android.graphics.Bitmap resource) {
                                imageViewThumbnail.setImageBitmap(resource);
                                showThumbnail();
                            }

                            @Override
                            public void onLoadFailed(android.graphics.drawable.Drawable errorDrawable) {
                                // Keep placeholder visible on error
                                Log.w(TAG, "Video thumbnail load failed for: " + mediaItem.getFileName());
                            }
                        });
            } else {
                // For images, load normally
                Glide.with(context)
                        .load(mediaItem.getUri())
                        .apply(PerformanceConfig.getThumbnailOptions())
                        .into(new com.bumptech.glide.request.target.ImageViewTarget<android.graphics.drawable.Drawable>(imageViewThumbnail) {
                            @Override
                            protected void setResource(android.graphics.drawable.Drawable resource) {
                                imageViewThumbnail.setImageDrawable(resource);
                                showThumbnail();
                            }

                            @Override
                            public void onLoadFailed(android.graphics.drawable.Drawable errorDrawable) {
                                // Keep placeholder visible on error
                                Log.w(TAG, "Image thumbnail load failed for: " + mediaItem.getFileName());
                            }
                        });
            }

            // Configure download button
            if (showDownloadButton) {
                buttonDownload.setVisibility(View.VISIBLE);
                buttonDownload.setOnClickListener(v -> {
                    if (downloadListener != null) {
                        downloadListener.onDownloadClick(mediaItem);
                    }
                });
            } else {
                buttonDownload.setVisibility(View.GONE);
            }

            // Set click listener for the entire item
            itemView.setOnClickListener(v -> {
                if (clickListener != null) {
                    clickListener.onMediaItemClick(mediaItem);
                }
            });
        }

        private void showThumbnail() {
            placeholderBackground.setVisibility(View.GONE);
            iconMediaType.setVisibility(View.GONE);
            imageViewThumbnail.setVisibility(View.VISIBLE);
        }

    }

    /**
     * DiffUtil callback for efficient updates
     */
    private static class MediaDiffCallback extends DiffUtil.Callback {
        private final List<MediaItem> oldList;
        private final List<MediaItem> newList;
        
        MediaDiffCallback(List<MediaItem> oldList, List<MediaItem> newList) {
            this.oldList = oldList;
            this.newList = newList;
        }
        
        @Override
        public int getOldListSize() {
            return oldList.size();
        }
        
        @Override
        public int getNewListSize() {
            return newList.size();
        }
        
        @Override
        public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
            MediaItem oldItem = oldList.get(oldItemPosition);
            MediaItem newItem = newList.get(newItemPosition);

            if (oldItem.getUri() == null && newItem.getUri() == null) return true;
            if (oldItem.getUri() == null || newItem.getUri() == null) return false;

            return oldItem.getUri().equals(newItem.getUri());
        }
        
        @Override
        public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
            MediaItem oldItem = oldList.get(oldItemPosition);
            MediaItem newItem = newList.get(newItemPosition);

            // Null-safe URI comparison
            boolean uriEquals = (oldItem.getUri() == null && newItem.getUri() == null) ||
                               (oldItem.getUri() != null && oldItem.getUri().equals(newItem.getUri()));

            // Null-safe filename comparison
            boolean fileNameEquals = (oldItem.getFileName() == null && newItem.getFileName() == null) ||
                                    (oldItem.getFileName() != null && oldItem.getFileName().equals(newItem.getFileName()));

            return uriEquals && fileNameEquals &&
                   oldItem.isVideo() == newItem.isVideo() &&
                   oldItem.getDateModified() == newItem.getDateModified();
        }
    }
}
