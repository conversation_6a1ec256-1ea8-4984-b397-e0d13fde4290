package com.ks.app.service.statussaver.utils;

import android.content.Context;
import com.bumptech.glide.Glide;
import com.bumptech.glide.GlideBuilder;
import com.bumptech.glide.load.DecodeFormat;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory;
import com.bumptech.glide.load.engine.cache.LruResourceCache;
import com.bumptech.glide.request.RequestOptions;

/**
 * Performance configuration for the entire app
 * Optimized for handling 1000+ media items efficiently
 */
public class PerformanceConfig {
    
    // Memory optimization constants
    private static final int MEMORY_CACHE_SIZE = 1024 * 1024 * 50; // 50MB
    private static final int DISK_CACHE_SIZE = 1024 * 1024 * 250; // 250MB
    private static final int THUMBNAIL_SIZE = 200; // 200x200 thumbnails
    
    /**
     * Configure Glide for optimal performance
     */
    public static void configureGlide(Context context, GlideBuilder builder) {
        // Memory cache
        builder.setMemoryCache(new LruResourceCache(MEMORY_CACHE_SIZE));
        
        // Disk cache
        builder.setDiskCache(new InternalCacheDiskCacheFactory(context, DISK_CACHE_SIZE));
        
        // Default request options
        RequestOptions defaultOptions = new RequestOptions()
                .format(DecodeFormat.PREFER_RGB_565) // Use less memory
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .skipMemoryCache(false)
                .override(THUMBNAIL_SIZE, THUMBNAIL_SIZE);
        
        builder.setDefaultRequestOptions(defaultOptions);
    }
    
    /**
     * Get optimal Glide request options for thumbnails
     * Follows definitive best practices for smooth rendering
     */
    public static RequestOptions getThumbnailOptions() {
        return new RequestOptions()
                .format(DecodeFormat.PREFER_RGB_565)           // Use less memory
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC) // Smart caching strategy
                .override(300, 300)                            // Resize to prevent OOM
                .centerCrop();
    }
    
    /**
     * Get optimal Glide request options for video thumbnails
     * Uses frame extraction at 1 second for consistent previews
     */
    public static RequestOptions getVideoThumbnailOptions() {
        return new RequestOptions()
                .format(DecodeFormat.PREFER_RGB_565)           // Use less memory
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC) // Smart caching strategy
                .override(300, 300)                            // Resize to prevent OOM
                .centerCrop()
                .frame(1000000); // Get frame at 1 second
    }
    
    /**
     * Get performance configuration values
     */
    public static class Config {
        public static final int RECYCLER_VIEW_CACHE_SIZE = 20;
        public static final int GRID_PREFETCH_COUNT = 6;
        public static final boolean ENABLE_NESTED_SCROLLING = false;
        public static final boolean RECYCLE_CHILDREN_ON_DETACH = true;
        public static final boolean ENABLE_ITEM_PREFETCH = true;
    }
}
