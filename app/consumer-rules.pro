# Consumer ProGuard rules for WhatsApp Status Saver
# These rules will be applied to any app that includes this as a library

# Keep public API classes
-keep public class com.ks.app.service.statussaver.utils.SimplifiedPermissionManager { *; }
-keep public class com.ks.app.service.statussaver.utils.ResponsiveTextUtils { *; }
-keep public class com.ks.app.service.statussaver.ui.base.PermissionAwareActivity { *; }
-keep public class com.ks.app.service.statussaver.ui.base.ResponsiveActivity { *; }
-keep public class com.ks.app.service.statussaver.ui.base.ResponsiveFragment { *; }

# Keep model classes
-keep class com.ks.app.service.statussaver.data.models.** { *; }

# Keep callback interfaces
-keep interface com.ks.app.service.statussaver.utils.SimplifiedPermissionManager$PermissionCallback { *; }
