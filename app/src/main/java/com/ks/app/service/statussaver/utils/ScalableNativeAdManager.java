package com.ks.app.service.statussaver.utils;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdLoader;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.nativead.NativeAd;
import com.google.android.gms.ads.nativead.NativeAdView;
import com.ks.app.service.statussaver.R;
import java.util.HashMap;
import java.util.Map;

/**
 * ✅ SCALABLE NATIVE AD MANAGER - Best Practice Implementation
 * 
 * Features:
 * - Responsive width (match_parent) for all devices
 * - Fixed height (140dp) for consistent design
 * - Clean separation with background
 * - Prominent CTA button placement
 * - Proper error handling and caching
 * - Easy to maintain and extend
 */
public class ScalableNativeAdManager {
    
    private static final String TAG = "ScalableNativeAdManager";
    private static final String NATIVE_AD_UNIT_ID = "ca-app-pub-7557152164205920/5890575132";
    
    // ✅ Ad caching for performance
    private static final Map<String, NativeAd> adCache = new HashMap<>();
    private static final Map<String, Long> adCacheTimestamps = new HashMap<>();
    private static final long CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
    
    /**
     * ✅ BEST PRACTICE: Load Native Ad with Scalable Approach
     * 
     * @param context Activity context
     * @param container FrameLayout container for the ad
     * @param screenName Screen identifier for caching
     * @param callback Optional callback for ad events
     */
    public static void loadNativeAd(Context context, FrameLayout container, 
                                  String screenName, NativeAdCallback callback) {
        
        if (context == null || container == null) {
            Log.w(TAG, "❌ Cannot load native ad - context or container is null");
            if (callback != null) callback.onAdFailedToLoad("Invalid parameters");
            return;
        }

        Log.d(TAG, "🚀 SCALABLE LOADING: Starting native ad load for " + screenName);
        Log.d(TAG, "📱 Ad Unit ID: " + NATIVE_AD_UNIT_ID);
        Log.d(TAG, "📏 Container: " + container.getWidth() + "x" + container.getHeight());

        // ✅ Check cache first for performance
        NativeAd cachedAd = getCachedAd(screenName);
        if (cachedAd != null) {
            Log.d(TAG, "⚡ CACHE HIT: Using cached ad for instant display");
            displayNativeAd(context, container, cachedAd, screenName, callback);
            return;
        }

        // ✅ Show shimmer while loading
        showShimmerLoading(context, container);

        // ✅ Create AdLoader with best practices
        AdLoader adLoader = new AdLoader.Builder(context, NATIVE_AD_UNIT_ID)
                .forNativeAd(nativeAd -> {
                    Log.d(TAG, "✅ AD LOADED: Native ad loaded successfully for " + screenName);
                    
                    // Cache for future use
                    cacheAd(screenName, nativeAd);
                    
                    // Display the ad
                    displayNativeAd(context, container, nativeAd, screenName, callback);
                })
                .withAdListener(new AdListener() {
                    @Override
                    public void onAdLoaded() {
                        Log.d(TAG, "📺 AD CALLBACK: onAdLoaded for " + screenName);
                        if (callback != null) callback.onAdLoaded();
                    }

                    @Override
                    public void onAdFailedToLoad(LoadAdError adError) {
                        Log.w(TAG, "❌ AD FAILED: " + adError.getMessage());
                        Log.w(TAG, "🔢 Error code: " + adError.getCode());
                        
                        // Hide shimmer and show fallback
                        hideShimmerAndShowFallback(container);
                        
                        if (callback != null) {
                            callback.onAdFailedToLoad(adError.getMessage());
                        }
                    }

                    @Override
                    public void onAdClicked() {
                        Log.d(TAG, "👆 AD CLICKED: User clicked ad in " + screenName);
                        
                        // Clear cache on click for fresh ads
                        clearCachedAd(screenName);
                        
                        if (callback != null) callback.onAdClicked();
                    }

                    @Override
                    public void onAdImpression() {
                        Log.d(TAG, "👁️ AD IMPRESSION: Recorded for " + screenName);
                        if (callback != null) callback.onAdImpression();
                    }
                })
                .build();

        // ✅ Load with optimized request
        AdRequest adRequest = new AdRequest.Builder().build();
        adLoader.loadAd(adRequest);
    }

    /**
     * ✅ BEST PRACTICE: Display Native Ad with Proper Binding
     */
    private static void displayNativeAd(Context context, FrameLayout container, 
                                      NativeAd nativeAd, String screenName, 
                                      NativeAdCallback callback) {
        
        Log.d(TAG, "🎨 DISPLAYING: Binding native ad data for " + screenName);

        // ✅ Inflate the scalable layout
        NativeAdView adView = (NativeAdView) LayoutInflater.from(context)
                .inflate(R.layout.native_ad_compact, null);

        // ✅ Populate with best practices
        populateNativeAdView(nativeAd, adView);

        // ✅ Replace shimmer with actual ad
        container.removeAllViews();
        container.addView(adView);

        Log.d(TAG, "✅ DISPLAYED: Native ad successfully displayed for " + screenName);
        
        if (callback != null) callback.onAdDisplayed();
    }

    /**
     * ✅ BEST PRACTICE: Populate Native Ad View
     * Following Google's guidelines for proper asset binding
     */
    private static void populateNativeAdView(NativeAd nativeAd, NativeAdView adView) {
        Log.d(TAG, "🔗 BINDING: Populating native ad assets");

        // ✅ Set view references
        adView.setHeadlineView(adView.findViewById(R.id.ad_headline));
        adView.setBodyView(adView.findViewById(R.id.ad_body));
        adView.setCallToActionView(adView.findViewById(R.id.ad_call_to_action));
        adView.setIconView(adView.findViewById(R.id.ad_app_icon));
        adView.setStarRatingView(adView.findViewById(R.id.ad_stars));
        adView.setAdvertiserView(adView.findViewById(R.id.ad_advertiser));
        adView.setPriceView(adView.findViewById(R.id.ad_price));

        // ✅ Ensure attribution is visible (Google Policy)
        TextView attribution = adView.findViewById(R.id.ad_attribution);
        if (attribution != null) {
            attribution.setVisibility(View.VISIBLE);
        }

        // ✅ Populate headline (required)
        if (nativeAd.getHeadline() != null) {
            ((TextView) adView.getHeadlineView()).setText(nativeAd.getHeadline());
        }

        // ✅ Populate body (optional)
        if (nativeAd.getBody() != null && !nativeAd.getBody().isEmpty()) {
            adView.getBodyView().setVisibility(View.VISIBLE);
            ((TextView) adView.getBodyView()).setText(nativeAd.getBody());
        } else {
            adView.getBodyView().setVisibility(View.GONE);
        }

        // ✅ Populate call to action (required)
        if (nativeAd.getCallToAction() != null) {
            ((Button) adView.getCallToActionView()).setText(nativeAd.getCallToAction());
        }

        // ✅ Populate icon
        if (nativeAd.getIcon() != null) {
            ((ImageView) adView.getIconView()).setImageDrawable(nativeAd.getIcon().getDrawable());
            adView.getIconView().setVisibility(View.VISIBLE);
        } else {
            adView.getIconView().setVisibility(View.GONE);
        }

        // ✅ Populate star rating
        if (nativeAd.getStarRating() != null) {
            ((RatingBar) adView.getStarRatingView()).setRating(nativeAd.getStarRating().floatValue());
            adView.getStarRatingView().setVisibility(View.VISIBLE);
        } else {
            adView.getStarRatingView().setVisibility(View.GONE);
        }

        // ✅ Populate advertiser
        if (nativeAd.getAdvertiser() != null && !nativeAd.getAdvertiser().isEmpty()) {
            ((TextView) adView.getAdvertiserView()).setText(nativeAd.getAdvertiser());
            adView.getAdvertiserView().setVisibility(View.VISIBLE);
        } else {
            adView.getAdvertiserView().setVisibility(View.GONE);
        }

        // ✅ Populate price
        if (nativeAd.getPrice() != null && !nativeAd.getPrice().isEmpty()) {
            ((TextView) adView.getPriceView()).setText(nativeAd.getPrice());
            adView.getPriceView().setVisibility(View.VISIBLE);
        } else {
            adView.getPriceView().setVisibility(View.GONE);
        }

        // ✅ Register the NativeAdView with the NativeAd object
        adView.setNativeAd(nativeAd);

        Log.d(TAG, "✅ BINDING COMPLETE: All assets populated successfully");
    }

    /**
     * ✅ Show Shimmer Loading
     */
    private static void showShimmerLoading(Context context, FrameLayout container) {
        View shimmerView = LayoutInflater.from(context)
                .inflate(R.layout.native_ad_shimmer, null);
        
        container.removeAllViews();
        container.addView(shimmerView);
        
        ShimmerFrameLayout shimmerContainer = shimmerView.findViewById(R.id.shimmer_container);
        if (shimmerContainer != null) {
            shimmerContainer.startShimmer();
        }
        
        Log.d(TAG, "✨ SHIMMER: Loading animation started");
    }

    /**
     * ✅ Hide Shimmer and Show Fallback
     */
    private static void hideShimmerAndShowFallback(FrameLayout container) {
        // Stop shimmer animation
        if (container.getChildCount() > 0) {
            View child = container.getChildAt(0);
            ShimmerFrameLayout shimmerContainer = child.findViewById(R.id.shimmer_container);
            if (shimmerContainer != null) {
                shimmerContainer.stopShimmer();
            }
        }
        
        // Hide the container
        container.setVisibility(View.GONE);
        
        Log.d(TAG, "🚫 FALLBACK: Shimmer stopped, container hidden");
    }

    // ✅ ========== CACHING METHODS ==========

    private static NativeAd getCachedAd(String screenName) {
        if (!adCache.containsKey(screenName)) return null;
        
        Long timestamp = adCacheTimestamps.get(screenName);
        if (timestamp == null || (System.currentTimeMillis() - timestamp) > CACHE_DURATION) {
            clearCachedAd(screenName);
            return null;
        }
        
        return adCache.get(screenName);
    }

    private static void cacheAd(String screenName, NativeAd nativeAd) {
        adCache.put(screenName, nativeAd);
        adCacheTimestamps.put(screenName, System.currentTimeMillis());
        Log.d(TAG, "💾 CACHED: Ad cached for " + screenName);
    }

    private static void clearCachedAd(String screenName) {
        NativeAd cachedAd = adCache.remove(screenName);
        adCacheTimestamps.remove(screenName);
        if (cachedAd != null) {
            cachedAd.destroy();
            Log.d(TAG, "🗑️ CACHE CLEARED: " + screenName);
        }
    }

    /**
     * ✅ Clear All Cached Ads
     */
    public static void clearAllCachedAds() {
        for (NativeAd ad : adCache.values()) {
            if (ad != null) ad.destroy();
        }
        adCache.clear();
        adCacheTimestamps.clear();
        Log.d(TAG, "🧹 ALL CACHE CLEARED");
    }

    /**
     * ✅ Destroy Native Ad
     */
    public static void destroyNativeAd(FrameLayout container) {
        if (container != null) {
            hideShimmerAndShowFallback(container);
            Log.d(TAG, "🗑️ DESTROYED: Native ad destroyed");
        }
    }

    /**
     * ✅ Callback Interface for Ad Events
     */
    public interface NativeAdCallback {
        default void onAdLoaded() {}
        default void onAdFailedToLoad(String error) {}
        default void onAdDisplayed() {}
        default void onAdClicked() {}
        default void onAdImpression() {}
    }
}
