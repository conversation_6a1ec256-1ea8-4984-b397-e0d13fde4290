<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/shimmer_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/card_background"
        android:padding="12dp"
        android:minHeight="120dp">

        <!-- App Icon Shimmer -->
        <View
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/shimmer_circle"
            android:layout_marginEnd="12dp" />

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical">

            <!-- Headline Shimmer -->
            <View
                android:layout_width="match_parent"
                android:layout_height="16dp"
                android:background="@drawable/shimmer_rectangle"
                android:layout_marginBottom="8dp" />

            <!-- Second line of headline -->
            <View
                android:layout_width="200dp"
                android:layout_height="16dp"
                android:background="@drawable/shimmer_rectangle"
                android:layout_marginBottom="12dp" />

            <!-- CTA Button Shimmer -->
            <View
                android:layout_width="80dp"
                android:layout_height="32dp"
                android:background="@drawable/shimmer_button" />

        </LinearLayout>

        <!-- Ad Badge Shimmer -->
        <View
            android:layout_width="24dp"
            android:layout_height="16dp"
            android:background="@drawable/shimmer_badge"
            android:layout_gravity="top|end" />

    </LinearLayout>

</com.facebook.shimmer.ShimmerFrameLayout>
