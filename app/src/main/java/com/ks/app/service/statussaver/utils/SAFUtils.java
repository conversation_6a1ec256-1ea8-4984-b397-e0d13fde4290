package com.ks.app.service.statussaver.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.DocumentsContract;
import android.util.Log;
import androidx.documentfile.provider.DocumentFile;
import com.ks.app.service.statussaver.data.models.MediaItem;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class SAFUtils {

    private static final String TAG = "SAFUtils";
    public static final int REQUEST_CODE_SAF_WHATSAPP_STATUSES = 1002;
    private static final String WHATSAPP_STATUSES_TREE_URI_KEY = "whatsapp_statuses_tree_uri";





    public static void persistFolderUri(Context context, Intent data, String key) {
        Uri treeUri = data.getData();
        if (treeUri != null) {
            SharedPreferences prefs = context.getSharedPreferences("SAF_PREFS", Context.MODE_PRIVATE);
            prefs.edit().putString(key, treeUri.toString()).apply();
            final int takeFlags = data.getFlags() & (Intent.FLAG_GRANT_READ_URI_PERMISSION | Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
            try {
                 context.getContentResolver().takePersistableUriPermission(treeUri, takeFlags);
                 Log.i(TAG, "Persisted folder URI for key " + key + ": " + treeUri.toString());
            } catch (SecurityException e) {
                Log.e(TAG, "Failed to take persistable URI permission for key " + key, e);
                prefs.edit().remove(key).apply();
            }
        }
    }

    public static Uri getPersistedFolderUri(Context context, String key) {
        SharedPreferences prefs = context.getSharedPreferences("SAF_PREFS", Context.MODE_PRIVATE);
        String uriString = prefs.getString(key, null);
        return uriString != null ? Uri.parse(uriString) : null;
    }

    public static boolean hasSAFAccess(Context context, String key) {
        return getPersistedFolderUri(context, key) != null;
    }



    public static String getWhatsAppStatusesTreeUriKey() {
        return WHATSAPP_STATUSES_TREE_URI_KEY;
    }



    // ✅ Clean Android Version-Specific Access Methods

    /**
     * Main method to access WhatsApp statuses based on Android version
     * Android 7-10: Uses File access
     * Android 11+: Uses SAF access
     * ✅ Includes comprehensive null safety
     */
    public static List<MediaItem> getWhatsAppStatuses(Context context) {
        try {
            // ✅ Null safety check
            if (context == null) {
                Log.w(TAG, "Context is null - cannot get WhatsApp statuses");
                return new ArrayList<>();
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ - Use SAF
                return getStatusesFromSAF(context);
            } else {
                // Android 7-10 - Use File access
                return getStatusesFromLegacyFile(context);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting WhatsApp statuses: " + e.getMessage(), e);
            return new ArrayList<>();
        } catch (Throwable t) {
            Log.e(TAG, "Unexpected error getting WhatsApp statuses: " + t.getMessage(), t);
            return new ArrayList<>();
        }
    }

    /**
     * Android 7-10: Direct file access to WhatsApp/Media/.Statuses
     * ✅ Includes comprehensive null safety
     */
    private static List<MediaItem> getStatusesFromLegacyFile(Context context) {
        List<MediaItem> mediaItems = new ArrayList<>();

        try {
            // ✅ Null safety checks
            if (context == null) {
                Log.w(TAG, "Context is null - cannot access legacy statuses");
                return mediaItems;
            }

            File baseDir = Environment.getExternalStorageDirectory();
            if (baseDir == null) {
                Log.w(TAG, "External storage directory is null");
                return mediaItems;
            }

            File statusesDir = new File(baseDir, "WhatsApp/Media/.Statuses");
            if (statusesDir == null) {
                Log.w(TAG, "Failed to create statuses directory reference");
                return mediaItems;
            }

            if (statusesDir.exists() && statusesDir.isDirectory()) {
                File[] files = statusesDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (file != null && file.isFile()) {
                            String fileName = file.getName();
                            if (fileName != null && !fileName.endsWith(".nomedia")) {
                                String lowerName = fileName.toLowerCase();

                                if (lowerName.endsWith(".jpg") || lowerName.endsWith(".png") || lowerName.endsWith(".mp4")) {
                                    try {
                                        Uri fileUri = Uri.fromFile(file);
                                        if (fileUri != null) {
                                            boolean isVideo = lowerName.endsWith(".mp4");
                                            long lastModified = file.lastModified();

                                            MediaItem mediaItem = new MediaItem(fileUri, fileName, isVideo, lastModified);
                                            if (mediaItem != null) {
                                                mediaItems.add(mediaItem);
                                            }
                                        }
                                    } catch (Exception e) {
                                        Log.w(TAG, "Error processing file " + fileName + ": " + e.getMessage());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (SecurityException e) {
            Log.w(TAG, "Security exception accessing legacy statuses: " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Error accessing legacy statuses: " + e.getMessage(), e);
        } catch (Throwable t) {
            Log.e(TAG, "Unexpected error accessing legacy statuses: " + t.getMessage(), t);
        }

        Log.d(TAG, "Found " + mediaItems.size() + " status items via legacy file access");
        return mediaItems;
    }

    /**
     * Android 11+: SAF access to Android/media/com.whatsapp/WhatsApp/Media/.Statuses
     * ✅ Includes comprehensive null safety
     */
    private static List<MediaItem> getStatusesFromSAF(Context context) {
        List<MediaItem> mediaItems = new ArrayList<>();

        try {
            // ✅ Null safety checks
            if (context == null) {
                Log.w(TAG, "Context is null - cannot access SAF statuses");
                return mediaItems;
            }

            String key = getWhatsAppStatusesTreeUriKey();
            if (key == null) {
                Log.w(TAG, "Tree URI key is null - cannot access SAF statuses");
                return mediaItems;
            }

            Uri folderUri = getPersistedFolderUri(context, key);
            if (folderUri == null) {
                Log.w(TAG, "No SAF access granted for Android 11+");
                return mediaItems;
            }

            DocumentFile root = DocumentFile.fromTreeUri(context, folderUri);
            if (root == null) {
                Log.e(TAG, "Invalid SAF root folder");
                return mediaItems;
            }

            // Navigate to .Statuses folder: Android/media/com.whatsapp/WhatsApp/Media/.Statuses
            String[] pathSegments = {"com.whatsapp", "WhatsApp", "Media", ".Statuses"};
            DocumentFile current = root;

            for (String segment : pathSegments) {
                if (segment == null) {
                    Log.w(TAG, "Path segment is null");
                    return mediaItems;
                }

                DocumentFile next = null;
                DocumentFile[] files = current.listFiles();
                if (files != null) {
                    for (DocumentFile file : files) {
                        if (file != null && file.getName() != null && file.getName().equalsIgnoreCase(segment)) {
                            next = file;
                            break;
                        }
                    }
                }

                if (next == null) {
                    Log.w(TAG, "Path segment not found: " + segment);
                    return mediaItems;
                }
                current = next;
            }

            // List media files in .Statuses folder
            DocumentFile[] statusFiles = current.listFiles();
            if (statusFiles != null) {
                for (DocumentFile file : statusFiles) {
                    if (file != null && file.isFile()) {
                        String fileName = file.getName();
                        if (fileName != null && !fileName.endsWith(".nomedia")) {
                            String lowerName = fileName.toLowerCase();

                            if (lowerName.endsWith(".jpg") || lowerName.endsWith(".png") || lowerName.endsWith(".mp4")) {
                                try {
                                    Uri fileUri = file.getUri();
                                    if (fileUri != null) {
                                        boolean isVideo = lowerName.endsWith(".mp4");
                                        long lastModified = file.lastModified();

                                        MediaItem mediaItem = new MediaItem(fileUri, fileName, isVideo, lastModified);
                                        if (mediaItem != null) {
                                            mediaItems.add(mediaItem);
                                        }
                                    }
                                } catch (Exception e) {
                                    Log.w(TAG, "Error processing SAF file " + fileName + ": " + e.getMessage());
                                }
                            }
                        }
                    }
                }
            }
        } catch (SecurityException e) {
            Log.w(TAG, "Security exception accessing SAF statuses: " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Error accessing SAF statuses: " + e.getMessage(), e);
        } catch (Throwable t) {
            Log.e(TAG, "Unexpected error accessing SAF statuses: " + t.getMessage(), t);
        }

        Log.d(TAG, "Found " + mediaItems.size() + " status items via SAF access");
        return mediaItems;
    }

    /**
     * Check if we have proper access to WhatsApp statuses based on Android version
     * ✅ Includes comprehensive null safety
     */
    public static boolean hasWhatsAppStatusAccess(Context context) {
        try {
            // ✅ Null safety check
            if (context == null) {
                Log.w(TAG, "Context is null - cannot check WhatsApp status access");
                return false;
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ - Check SAF access
                String key = getWhatsAppStatusesTreeUriKey();
                if (key == null) {
                    Log.w(TAG, "Tree URI key is null - cannot check SAF access");
                    return false;
                }
                return hasSAFAccess(context, key);
            } else {
                // Android 7-10 - Check file access
                try {
                    File baseDir = Environment.getExternalStorageDirectory();
                    if (baseDir == null) {
                        Log.w(TAG, "External storage directory is null");
                        return false;
                    }
                    File statusesDir = new File(baseDir, "WhatsApp/Media/.Statuses");
                    return statusesDir != null && statusesDir.exists() && statusesDir.canRead();
                } catch (SecurityException e) {
                    Log.w(TAG, "Security exception checking file access: " + e.getMessage());
                    return false;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking WhatsApp status access: " + e.getMessage(), e);
            return false;
        } catch (Throwable t) {
            Log.e(TAG, "Unexpected error checking WhatsApp status access: " + t.getMessage(), t);
            return false;
        }
    }

    /**
     * Request SAF access for Android 11+ (opens Android/media picker)
     * ✅ Includes comprehensive null safety
     */
    public static void requestSAFAccess(Activity activity) {
        try {
            // ✅ Null safety check
            if (activity == null) {
                Log.w(TAG, "Activity is null - cannot request SAF access");
                return;
            }

            if (activity.isFinishing() || activity.isDestroyed()) {
                Log.w(TAG, "Activity is finishing or destroyed - cannot request SAF access");
                return;
            }

            Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT_TREE);
            if (intent == null) {
                Log.e(TAG, "Failed to create SAF intent");
                return;
            }

            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION |
                           Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION);

            // Guide user to Android/media folder
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                try {
                    Uri initialUri = DocumentsContract.buildDocumentUri(
                        "com.android.externalstorage.documents",
                        "primary:Android/media"
                    );
                    if (initialUri != null) {
                        intent.putExtra(DocumentsContract.EXTRA_INITIAL_URI, initialUri);
                    }
                } catch (Exception e) {
                    Log.w(TAG, "Could not set initial URI for SAF picker: " + e.getMessage(), e);
                }
            }

            activity.startActivityForResult(intent, REQUEST_CODE_SAF_WHATSAPP_STATUSES);
        } catch (Exception e) {
            Log.e(TAG, "Error requesting SAF access: " + e.getMessage(), e);
        } catch (Throwable t) {
            Log.e(TAG, "Unexpected error requesting SAF access: " + t.getMessage(), t);
        }
    }
}