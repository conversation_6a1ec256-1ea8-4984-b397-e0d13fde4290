package com.ks.app.service.statussaver.utils;

import android.app.Activity;
import android.content.Context;
import android.util.Log;

/**
 * ✅ SavePro Ad Strategy Implementation
 * Implements the specific ad placement strategy for SavePro – Status Downloader
 *
 * Ad Strategy:
 * 1. Interstitial ad shown after splash (on first user action)
 * 2. Native ads on home screen, image preview, and video preview
 */
public class SaveProAdStrategy {

    private static final String TAG = "SaveProAdStrategy";
    
    // ✅ Production Ad Unit IDs
    public static final String APP_ID = "ca-app-pub-7557152164205920~6417975169";
    public static final String INTERSTITIAL_AFTER_SPLASH = "ca-app-pub-7557152164205920/6418571631";
    public static final String NATIVE_AD_UNIT = "ca-app-pub-7557152164205920/5890575132";
    
    // ✅ Ad State Management
    private static boolean hasShownPostSplashInterstitial = false;
    private static InterstitialAdManager interstitialManager;
    
    /**
     * ✅ Initialize Ad Strategy
     * Call this in your Application class
     */
    public static void initialize(Context context) {
        Log.d(TAG, "Initializing SavePro Ad Strategy");
        
        // Initialize AdMob
        AdMobConfig.initialize(context);
        
        // Initialize interstitial manager
        interstitialManager = new InterstitialAdManager();
        interstitialManager.setCallback(new InterstitialAdManager.InterstitialAdCallback() {
            @Override
            public void onAdLoaded() {
                Log.d(TAG, "Post-splash interstitial ad loaded and ready");
            }
            
            @Override
            public void onAdFailedToLoad(String error) {
                Log.w(TAG, "Post-splash interstitial ad failed to load: " + error);
            }
            
            @Override
            public void onAdShown() {
                Log.d(TAG, "Post-splash interstitial ad shown");
                hasShownPostSplashInterstitial = true;
            }
            
            @Override
            public void onAdDismissed() {
                Log.d(TAG, "Post-splash interstitial ad dismissed");
                // Don't reload - this is a one-time ad after splash
            }
            
            @Override
            public void onAdFailedToShow(String error) {
                Log.w(TAG, "Post-splash interstitial ad failed to show: " + error);
            }
        });
        
        // Pre-load the post-splash interstitial ad
        loadPostSplashInterstitial(context);
    }
    
    /**
     * ✅ Load Post-Splash Interstitial Ad
     * Loads the interstitial ad that will be shown after splash
     */
    public static void loadPostSplashInterstitial(Context context) {
        if (!hasShownPostSplashInterstitial && interstitialManager != null) {
            Log.d(TAG, "Loading post-splash interstitial ad");
            interstitialManager.loadAd(context);
        }
    }
    
    /**
     * ✅ Show Post-Splash Interstitial Ad
     * Call this on the first user action after splash screen
     * Returns true if ad was shown, false otherwise
     */
    public static boolean showPostSplashInterstitial(Activity activity) {
        if (hasShownPostSplashInterstitial) {
            Log.d(TAG, "Post-splash interstitial already shown");
            return false;
        }
        
        if (interstitialManager != null && interstitialManager.isAdLoaded()) {
            Log.d(TAG, "Showing post-splash interstitial ad");
            return interstitialManager.showAd(activity);
        } else {
            Log.w(TAG, "Post-splash interstitial ad not ready");
            return false;
        }
    }
    

    
    /**
     * ✅ Check if Post-Splash Interstitial Has Been Shown
     */
    public static boolean hasShownPostSplashInterstitial() {
        return hasShownPostSplashInterstitial;
    }
    
    /**
     * ✅ Reset Post-Splash Interstitial State
     * Call this if you want to reset the state (e.g., for testing)
     */
    public static void resetPostSplashInterstitialState() {
        Log.d(TAG, "Resetting post-splash interstitial state");
        hasShownPostSplashInterstitial = false;
    }
    
    /**
     * ✅ Lifecycle Management
     * Call these methods in your activities for proper ad lifecycle management
     */
    public static class LifecycleHelper {

        /**
         * Call in Activity onDestroy()
         */
        public static void onActivityDestroy() {
            // Clean up interstitial manager if needed
            if (interstitialManager != null) {
                interstitialManager.destroy();
            }
        }
    }
    
    /**
     * ✅ Usage Instructions:
     * 
     * 1. In SaveProApplication.onCreate():
     *    SaveProAdStrategy.initialize(this);
     * 
     * 2. In SplashActivity (or first activity after splash):
     *    // On first user action (button click, etc.)
     *    SaveProAdStrategy.showPostSplashInterstitial(this);
     * 
     * 3. In MainActivity.onCreate():
     *    AdView bannerAdView = findViewById(R.id.banner_ad_view);
     *    SaveProAdStrategy.setupHomeScreenBanner(bannerAdView);
     * 
     * 4. In MainActivity lifecycle methods:
     *    @Override
     *    protected void onPause() {
     *        super.onPause();
     *        SaveProAdStrategy.LifecycleHelper.onActivityPause(bannerAdView);
     *    }
     *    
     *    @Override
     *    protected void onResume() {
     *        super.onResume();
     *        SaveProAdStrategy.LifecycleHelper.onActivityResume(bannerAdView);
     *    }
     *    
     *    @Override
     *    protected void onDestroy() {
     *        super.onDestroy();
     *        SaveProAdStrategy.LifecycleHelper.onActivityDestroy(bannerAdView);
     *    }
     * 
     * 5. Layout for MainActivity (add to your main layout):
     *    <com.google.android.gms.ads.AdView
     *        android:id="@+id/banner_ad_view"
     *        android:layout_width="wrap_content"
     *        android:layout_height="wrap_content"
     *        android:layout_alignParentBottom="true"
     *        android:layout_centerHorizontal="true"
     *        ads:adSize="BANNER"
     *        ads:adUnitId="@string/banner_ad_unit_id" />
     */
}
