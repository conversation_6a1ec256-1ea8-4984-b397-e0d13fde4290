package com.ks.app.service.statussaver.utils;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;

/**
 * ✅ Interstitial Ad Manager
 * Advanced interstitial ad management following Google AdMob best practices
 * Updated according to https://developers.google.com/admob/android/interstitial
 */
public class InterstitialAdManager {

    private static final String TAG = "InterstitialAdManager";
    
    // ✅ Production Ad Unit ID - Interstitial (After Splash - First User Action)
    private static final String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-7557152164205920/6418571631";
    
    // ✅ Ad State Management
    private InterstitialAd interstitialAd;
    private boolean isLoading = false;
    private long lastShowTime = 0;
    private static final long MIN_INTERVAL_MS = 60000; // 1 minute between ads
    
    // ✅ Callback Interface
    public interface InterstitialAdCallback {
        void onAdLoaded();
        void onAdFailedToLoad(String error);
        void onAdShown();
        void onAdDismissed();
        void onAdFailedToShow(String error);
    }
    
    private InterstitialAdCallback callback;
    
    /**
     * ✅ Constructor
     */
    public InterstitialAdManager() {
        // Initialize with default values
    }
    
    /**
     * ✅ Set Callback
     */
    public void setCallback(InterstitialAdCallback callback) {
        this.callback = callback;
    }
    
    /**
     * ✅ Load Interstitial Ad
     * Following latest Google AdMob guidelines
     */
    public void loadAd(Context context) {
        if (isLoading) {
            Log.d(TAG, "Ad is already loading");
            return;
        }
        
        if (interstitialAd != null) {
            Log.d(TAG, "Ad already loaded");
            return;
        }
        
        isLoading = true;
        Log.d(TAG, "Loading interstitial ad...");
        
        AdRequest adRequest = new AdRequest.Builder().build();
        
        InterstitialAd.load(context, INTERSTITIAL_AD_UNIT_ID, adRequest,
                new InterstitialAdLoadCallback() {
                    @Override
                    public void onAdLoaded(InterstitialAd ad) {
                        Log.d(TAG, "Interstitial ad loaded successfully");
                        interstitialAd = ad;
                        isLoading = false;
                        
                        // Set up full screen content callback
                        setupFullScreenContentCallback();
                        
                        if (callback != null) {
                            callback.onAdLoaded();
                        }
                    }
                    
                    @Override
                    public void onAdFailedToLoad(LoadAdError loadAdError) {
                        Log.w(TAG, "Interstitial ad failed to load");
                        Log.w(TAG, "Error code: " + loadAdError.getCode());
                        Log.w(TAG, "Error message: " + loadAdError.getMessage());
                        Log.w(TAG, "Error domain: " + loadAdError.getDomain());
                        
                        interstitialAd = null;
                        isLoading = false;
                        
                        if (callback != null) {
                            callback.onAdFailedToLoad(loadAdError.getMessage());
                        }
                    }
                });
    }
    
    /**
     * ✅ Setup Full Screen Content Callback
     * Handles ad lifecycle events
     */
    private void setupFullScreenContentCallback() {
        if (interstitialAd == null) return;
        
        interstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
            @Override
            public void onAdClicked() {
                Log.d(TAG, "Interstitial ad was clicked");
            }
            
            @Override
            public void onAdDismissedFullScreenContent() {
                Log.d(TAG, "Interstitial ad dismissed");
                interstitialAd = null;
                
                if (callback != null) {
                    callback.onAdDismissed();
                }
            }
            
            @Override
            public void onAdFailedToShowFullScreenContent(AdError adError) {
                Log.w(TAG, "Interstitial ad failed to show: " + adError.getMessage());
                interstitialAd = null;
                
                if (callback != null) {
                    callback.onAdFailedToShow(adError.getMessage());
                }
            }
            
            @Override
            public void onAdImpression() {
                Log.d(TAG, "Interstitial ad recorded an impression");
            }
            
            @Override
            public void onAdShowedFullScreenContent() {
                Log.d(TAG, "Interstitial ad showed full screen content");
                lastShowTime = System.currentTimeMillis();
                
                if (callback != null) {
                    callback.onAdShown();
                }
            }
        });
    }
    
    /**
     * ✅ Show Interstitial Ad
     * Shows the ad if loaded and respects frequency capping
     */
    public boolean showAd(Activity activity) {
        if (interstitialAd == null) {
            Log.w(TAG, "Interstitial ad not loaded");
            return false;
        }
        
        if (!canShowAd()) {
            Log.w(TAG, "Cannot show ad - frequency limit reached");
            return false;
        }
        
        Log.d(TAG, "Showing interstitial ad");
        interstitialAd.show(activity);
        return true;
    }
    
    /**
     * ✅ Check if Ad Can Be Shown
     * Implements frequency capping
     */
    public boolean canShowAd() {
        long currentTime = System.currentTimeMillis();
        return (currentTime - lastShowTime) >= MIN_INTERVAL_MS;
    }
    
    /**
     * ✅ Check if Ad is Loaded
     */
    public boolean isAdLoaded() {
        return interstitialAd != null;
    }
    
    /**
     * ✅ Check if Ad is Loading
     */
    public boolean isAdLoading() {
        return isLoading;
    }
    
    /**
     * ✅ Get Time Until Next Ad Can Be Shown
     * Returns milliseconds until next ad can be shown
     */
    public long getTimeUntilNextAd() {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastAd = currentTime - lastShowTime;
        return Math.max(0, MIN_INTERVAL_MS - timeSinceLastAd);
    }
    
    /**
     * ✅ Destroy
     * Clean up resources
     */
    public void destroy() {
        interstitialAd = null;
        callback = null;
        isLoading = false;
    }
    
    /**
     * ✅ Usage Example:
     * 
     * // In your Activity:
     * InterstitialAdManager adManager = new InterstitialAdManager();
     * 
     * // Set callback (optional)
     * adManager.setCallback(new InterstitialAdManager.InterstitialAdCallback() {
     *     @Override
     *     public void onAdLoaded() {
     *         Log.d("MyApp", "Interstitial ad loaded");
     *     }
     *     
     *     @Override
     *     public void onAdFailedToLoad(String error) {
     *         Log.w("MyApp", "Failed to load ad: " + error);
     *     }
     *     
     *     @Override
     *     public void onAdShown() {
     *         Log.d("MyApp", "Interstitial ad shown");
     *     }
     *     
     *     @Override
     *     public void onAdDismissed() {
     *         Log.d("MyApp", "Interstitial ad dismissed");
     *         // Load next ad
     *         adManager.loadAd(this);
     *     }
     *     
     *     @Override
     *     public void onAdFailedToShow(String error) {
     *         Log.w("MyApp", "Failed to show ad: " + error);
     *     }
     * });
     * 
     * // Load ad
     * adManager.loadAd(this);
     * 
     * // Show ad when appropriate
     * if (adManager.isAdLoaded() && adManager.canShowAd()) {
     *     adManager.showAd(this);
     * }
     * 
     * // Clean up in onDestroy()
     * adManager.destroy();
     */
}
