<?xml version="1.0" encoding="utf-8"?>
<!-- ✅ FIXED NATIVE AD LAYOUT - No Hiding, No Resizing, FIXED HEIGHT -->
<com.google.android.gms.ads.nativead.NativeAdView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_ad_view"
    android:layout_width="match_parent"
    android:layout_height="140dp"
    android:layout_margin="8dp"
    android:background="#FFFFFF"
    android:elevation="2dp"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:orientation="vertical">

        <!-- Top Row: Ad Attribution - FIXED HEIGHT -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:orientation="horizontal"
            android:layout_marginBottom="4dp">

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/ad_attribution"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="#E0E0E0"
                android:padding="4dp"
                android:text="Ad"
                android:textColor="#666666"
                android:textSize="10sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Main Content Row - FIXED HEIGHT -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="116dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- App Icon - FIXED SIZE, NO RESIZING -->
            <FrameLayout
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginEnd="12dp"
                android:layout_gravity="center_vertical">

                <ImageView
                    android:id="@+id/ad_app_icon"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:contentDescription="App Icon"
                    android:scaleType="fitCenter"
                    android:background="#F0F0F0" />

            </FrameLayout>

            <!-- Content Section - FIXED HEIGHT, NO RESIZING -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:layout_marginEnd="12dp"
                android:orientation="vertical"
                android:gravity="center_vertical">

                <!-- Headline - FIXED HEIGHT -->
                <TextView
                    android:id="@+id/ad_headline"
                    android:layout_width="match_parent"
                    android:layout_height="18dp"
                    android:textColor="#000000"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:text="App Headline Here" />

                <!-- Body Text - FIXED HEIGHT -->
                <TextView
                    android:id="@+id/ad_body"
                    android:layout_width="match_parent"
                    android:layout_height="16dp"
                    android:textColor="#666666"
                    android:textSize="12sp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:visibility="visible"
                    android:text="App description text here" />

                <!-- Advertiser - FIXED HEIGHT -->
                <TextView
                    android:id="@+id/ad_advertiser"
                    android:layout_width="match_parent"
                    android:layout_height="14dp"
                    android:textColor="#888888"
                    android:textSize="11sp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:visibility="visible"
                    android:text="Advertiser Name" />

                <!-- Star Rating - FIXED HEIGHT -->
                <RatingBar
                    android:id="@+id/ad_stars"
                    style="?android:attr/ratingBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="16dp"
                    android:isIndicator="true"
                    android:numStars="5"
                    android:stepSize="0.5"
                    android:rating="4.5"
                    android:visibility="visible" />

                <!-- Price - FIXED HEIGHT -->
                <TextView
                    android:id="@+id/ad_price"
                    android:layout_width="wrap_content"
                    android:layout_height="16dp"
                    android:textColor="#1E88E5"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:gravity="center_vertical"
                    android:visibility="visible"
                    android:text="Free" />

            </LinearLayout>

            <!-- Call to Action Button - FIXED SIZE, NO RESIZING -->
            <FrameLayout
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center_vertical">

                <Button
                    android:id="@+id/ad_call_to_action"
                    android:layout_width="80dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center"
                    android:backgroundTint="#1E88E5"
                    android:text="Install"
                    android:textColor="#FFFFFF"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </FrameLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.gms.ads.nativead.NativeAdView>
