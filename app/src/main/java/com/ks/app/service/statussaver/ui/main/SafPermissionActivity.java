package com.ks.app.service.statussaver.ui.main;

import android.animation.ObjectAnimator;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.utils.SAFUtils;

public class SafPermissionActivity extends AppCompatActivity {

    private static final String TAG = "SafPermissionActivity";
    private ImageView handClickAnimation;
    private Handler animationHandler;
    private Runnable handClickRunnable;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Setup full screen
        setupFullScreen();
        
        setContentView(R.layout.activity_saf_permission);
        
        // Initialize views
        Button grantAccessButton = findViewById(R.id.button_allow_access);
        handClickAnimation = findViewById(R.id.hand_click_animation);
        animationHandler = new Handler(Looper.getMainLooper());

        // Setup button click listener
        grantAccessButton.setOnClickListener(v -> {
            Log.d(TAG, "Grant Access button clicked");
            handleGrantAccess();
        });

        // Start hand click animation
        startHandClickAnimation();
    }

    /**
     * ✅ Setup Full Screen Experience
     */
    private void setupFullScreen() {
        // Hide status bar and navigation bar for full immersive experience
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ approach
            getWindow().setDecorFitsSystemWindows(false);
        } else {
            // Legacy approach for older versions
            getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            );
        }
        
        // Set status bar color to black
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(ContextCompat.getColor(this, android.R.color.black));
            window.setNavigationBarColor(ContextCompat.getColor(this, android.R.color.black));
        }
    }

    /**
     * ✅ Handle Grant Access Button Click
     */
    private void handleGrantAccess() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ - Request SAF access
                SAFUtils.requestSAFAccess(this);
            } else {
                // Android 7-10 - File access is automatic with permissions
                finishWithSuccess();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling grant access: " + e.getMessage(), e);
            finishWithError();
        }
    }

    /**
     * ✅ Finish Activity with Success Result
     */
    private void finishWithSuccess() {
        Intent resultIntent = new Intent();
        resultIntent.putExtra("permission_granted", true);
        setResult(RESULT_OK, resultIntent);
        finish();
    }

    /**
     * ✅ Finish Activity with Error Result
     */
    private void finishWithError() {
        Intent resultIntent = new Intent();
        resultIntent.putExtra("permission_granted", false);
        setResult(RESULT_CANCELED, resultIntent);
        finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == SAFUtils.REQUEST_CODE_SAF_WHATSAPP_STATUSES) {
            if (resultCode == RESULT_OK && data != null) {
                try {
                    // Handle SAF permission result
                    SAFUtils.persistFolderUri(this, data, SAFUtils.getWhatsAppStatusesTreeUriKey());
                    Log.d(TAG, "SAF permission granted successfully");
                    finishWithSuccess();
                } catch (Exception e) {
                    Log.e(TAG, "Error handling SAF result: " + e.getMessage(), e);
                    finishWithError();
                }
            } else {
                Log.d(TAG, "SAF permission denied or cancelled");
                finishWithError();
            }
        }
    }

    /**
     * ✅ Start Hand Click Animation on USE THIS FOLDER button
     */
    private void startHandClickAnimation() {
        if (handClickAnimation != null) {
            // Show hand click animation after 2 seconds
            handClickRunnable = () -> {
                handClickAnimation.setVisibility(View.VISIBLE);

                // Create pulsing click animation
                ObjectAnimator scaleX = ObjectAnimator.ofFloat(handClickAnimation, "scaleX", 1.0f, 1.3f, 1.0f);
                ObjectAnimator scaleY = ObjectAnimator.ofFloat(handClickAnimation, "scaleY", 1.0f, 1.3f, 1.0f);
                ObjectAnimator alpha = ObjectAnimator.ofFloat(handClickAnimation, "alpha", 0.9f, 0.5f, 0.9f);

                // Set repeat count on individual animators
                scaleX.setRepeatCount(ValueAnimator.INFINITE);
                scaleY.setRepeatCount(ValueAnimator.INFINITE);
                alpha.setRepeatCount(ValueAnimator.INFINITE);

                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playTogether(scaleX, scaleY, alpha);
                animatorSet.setDuration(1000); // Faster click animation
                animatorSet.start();
            };

            animationHandler.postDelayed(handClickRunnable, 2000);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Clean up animation handler
        if (animationHandler != null && handClickRunnable != null) {
            animationHandler.removeCallbacks(handClickRunnable);
        }
    }

    @Override
    public void onBackPressed() {
        // Handle back button press
        finishWithError();
    }
}
