<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".ui.videos.VideoPreviewActivity">

    <!-- Top Header with Back Button Only -->
    <ImageButton
        android:id="@+id/button_back"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="16dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_arrow_back_24"
        android:tint="@android:color/white"
        android:contentDescription="@string/back_button"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Fixed Height Container for Consistent Sizing -->
    <FrameLayout
        android:id="@+id/media_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        app:layout_constraintTop_toBottomOf="@+id/button_back"
        app:layout_constraintBottom_toTopOf="@+id/native_ad_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- ExoPlayer StyledPlayerView for optimized video playback -->
        <com.google.android.exoplayer2.ui.StyledPlayerView
            android:id="@+id/video_view_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            app:use_controller="true"
            app:auto_show="false"
            app:show_buffering="when_playing"
            app:resize_mode="fit" />

        <!-- Centered Play Button -->
        <ImageButton
            android:id="@+id/button_play_video"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_gravity="center"
            android:background="@drawable/ic_play_circle"
            android:contentDescription="@string/play_video"
            android:scaleType="fitCenter"
            android:visibility="visible" />

    </FrameLayout>

    <!-- Native Ad Container - 20% HEIGHT -->
    <FrameLayout
        android:id="@+id/native_ad_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintHeight_percent="0.20"
        app:layout_constraintBottom_toTopOf="@+id/linearLayout_actions_video"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Bottom Actions with Fixed Height -->
    <LinearLayout
        android:id="@+id/linearLayout_actions_video"
        android:layout_width="0dp"
        android:layout_height="80dp"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="#80000000"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Button
            android:id="@+id/button_download_preview_video"
            style="@style/Widget.MaterialComponents.Button.TextButton.Icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/download"
            android:textColor="@android:color/white"
            app:icon="@android:drawable/stat_sys_download"
            app:iconTint="@android:color/white" />

        <Button
            android:id="@+id/button_share_preview_video"
            style="@style/Widget.MaterialComponents.Button.TextButton.Icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:text="@string/share"
            android:textColor="@android:color/white"
            app:icon="@android:drawable/ic_menu_share"
            app:iconTint="@android:color/white" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>