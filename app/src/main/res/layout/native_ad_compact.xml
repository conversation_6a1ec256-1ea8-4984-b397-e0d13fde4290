<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.nativead.NativeAdView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_ad_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#FFFFFF"
    android:layout_margin="8dp"
    android:padding="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#F8F9FA"
        android:padding="8dp">

        <!-- ✅ AD ATTRIBUTION LABEL - Auto Align -->
        <TextView
            android:id="@+id/ad_attribution"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ad"
            android:textColor="#666666"
            android:background="#E0E0E0"
            android:padding="4dp"
            android:textStyle="bold"
            android:layout_gravity="end" />

        <LinearLayout
            android:id="@+id/ad_content_container"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <!-- App Icon -->
        <ImageView
            android:id="@+id/ad_app_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="8dp"
            android:scaleType="centerCrop" />

        <!-- Content Section -->
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical">

            <!-- Headline -->
            <TextView
                android:id="@+id/ad_headline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <!-- Advertiser -->
            <TextView
                android:id="@+id/ad_advertiser"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:visibility="gone" />

            <!-- Body Text -->
            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/text_primary"
                android:visibility="gone" />

            <!-- Call to Action Button -->
            <Button
                android:id="@+id/ad_call_to_action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:background="@drawable/cta_button_background"
                android:padding="8dp"
                android:text="Install" />

        </LinearLayout>

        <!-- Right Section -->
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp">

            <!-- Ad Badge -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ad"
                android:textSize="10sp"
                android:textColor="@color/white"
                android:background="@color/ad_badge_color"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:layout_gravity="end" />

            <!-- Star Rating (if available) -->
            <LinearLayout
                android:id="@+id/ad_stars_container"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="4dp"
                android:visibility="gone">

                <RatingBar
                    android:id="@+id/ad_stars"
                    style="?android:attr/ratingBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:isIndicator="true"
                    android:numStars="5"
                    android:stepSize="0.5" />

            </LinearLayout>

            <!-- Price (if available) -->
            <TextView
                android:id="@+id/ad_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@color/price_color"
                android:layout_gravity="end"
                android:layout_marginTop="4dp"
                android:visibility="gone" />

        </LinearLayout>

        </LinearLayout>

    </RelativeLayout>

</com.google.android.gms.ads.nativead.NativeAdView>
