<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Base application theme. -->
    <style name="Theme.SmartStatusSaver" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorPrimaryVariant</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/statusBarColor</item>
        <!-- Optional: Make navigation bar transparent or match surface -->
        <!-- <item name="android:navigationBarColor">?android:attr/colorBackground</item> -->
        <!-- Optional: Ensure status bar icons are light or dark based on background -->
        <item name="android:windowLightStatusBar" tools:targetApi="m">false</item> <!-- false for light status bar icons (dark background), true for dark icons (light background) -->

        <!-- Customize your theme here. -->
        <item name="colorSurface">@color/black</item> <!-- Explicitly setting surface to black -->
        <item name="android:colorBackground">@color/black</item> <!-- Setting main window background to black -->
        <item name="colorOnSurface">@color/white</item>
        <item name="colorOnBackground">@color/white</item>
        <item name="android:textAppearance">@style/AppTextWhite</item>
    </style>

    <style name="Theme.SettingsStatusBar" parent="Theme.SmartStatusSaver">
        <item name="android:statusBarColor">@color/settingsStatusBarColor</item>
    </style>

    <style name="Theme.StatusSaver.NoActionBar" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:statusBarColor">#02473B</item>
    </style>
</resources>