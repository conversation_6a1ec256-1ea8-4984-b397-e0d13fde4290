package com.ks.app.service.statussaver.utils;

import android.content.Context;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.ks.app.service.statussaver.R;
import com.ks.app.service.statussaver.utils.PerformanceConfig;

/**
 * Utility class for consistent grid layout configuration across all tabs
 * Optimized for high performance with large datasets
 */
public class GridUtils {

    /**
     * Creates an optimized GridLayoutManager for all tabs
     * @param context The context
     * @return GridLayoutManager with appropriate span count and optimizations
     */
    public static GridLayoutManager createOptimizedGridLayoutManager(Context context) {
        int spanCount = context.getResources().getInteger(R.integer.grid_span_count_default);
        GridLayoutManager layoutManager = new GridLayoutManager(context, spanCount);

        // Performance optimizations using config
        layoutManager.setRecycleChildrenOnDetach(PerformanceConfig.Config.RECYCLE_CHILDREN_ON_DETACH);
        layoutManager.setItemPrefetchEnabled(PerformanceConfig.Config.ENABLE_ITEM_PREFETCH);
        layoutManager.setInitialPrefetchItemCount(PerformanceConfig.Config.GRID_PREFETCH_COUNT);

        return layoutManager;
    }

    /**
     * Creates a consistent GridLayoutManager for all tabs (legacy method)
     * @param context The context
     * @return GridLayoutManager with appropriate span count for the device
     */
    public static GridLayoutManager createGridLayoutManager(Context context) {
        return createOptimizedGridLayoutManager(context);
    }

    /**
     * Gets the span count for the current device configuration
     * @param context The context
     * @return The appropriate span count
     */
    public static int getSpanCount(Context context) {
        return context.getResources().getInteger(R.integer.grid_span_count_default);
    }

    /**
     * Optimizes RecyclerView for large datasets using performance config
     * @param recyclerView The RecyclerView to optimize
     */
    /**
     * Apply best practice optimizations for large media lists
     * Follows definitive architecture for smooth, crash-free rendering
     */
    public static void optimizeRecyclerView(RecyclerView recyclerView) {
        // Core performance optimizations
        recyclerView.setHasFixedSize(true);              // Fixed size for better performance
        recyclerView.setItemAnimator(null);              // Disable animations for smooth scrolling
        recyclerView.setItemViewCacheSize(10);           // Optimal cache size for memory efficiency
        recyclerView.setNestedScrollingEnabled(false);   // Disable nested scrolling for performance

        // Advanced optimizations for large datasets
        recyclerView.getRecycledViewPool().setMaxRecycledViews(0, 20);

        // Disable drawing cache (deprecated and can cause OOM)
        recyclerView.setDrawingCacheEnabled(false);

        // Enable view recycling optimizations
        if (recyclerView.getLayoutManager() instanceof androidx.recyclerview.widget.GridLayoutManager) {
            androidx.recyclerview.widget.GridLayoutManager gridManager =
                (androidx.recyclerview.widget.GridLayoutManager) recyclerView.getLayoutManager();
            gridManager.setRecycleChildrenOnDetach(true);
        }
    }


}
