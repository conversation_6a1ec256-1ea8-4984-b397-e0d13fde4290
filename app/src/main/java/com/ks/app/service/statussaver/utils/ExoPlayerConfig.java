package com.ks.app.service.statussaver.utils;

import android.content.Context;
import android.os.Build;
import com.google.android.exoplayer2.DefaultLoadControl;
import com.google.android.exoplayer2.DefaultRenderersFactory;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.LoadControl;
import com.google.android.exoplayer2.RenderersFactory;
import com.google.android.exoplayer2.trackselection.AdaptiveTrackSelection;
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
import com.google.android.exoplayer2.trackselection.TrackSelection;
import com.google.android.exoplayer2.trackselection.TrackSelector;
import com.google.android.exoplayer2.upstream.DefaultBandwidthMeter;

/**
 * ✅ ExoPlayer Configuration for Optimized Video Playback
 * Implements memory-efficient settings to prevent OutOfMemoryError
 * Based on official ExoPlayer documentation and best practices
 */
public class ExoPlayerConfig {

    /**
     * ⚙️ Optimized Buffer Configuration
     * Implements Drip-Feeding technique for consistent buffer management
     */
    public static class BufferConfig {
        // Drip-Feeding Settings (Recommended)
        public static final int MIN_BUFFER_MS = 20000;              // 20 seconds minimum buffer
        public static final int MAX_BUFFER_MS = 20000;              // 20 seconds maximum buffer
        public static final int BUFFER_FOR_PLAYBACK_MS = 1000;      // 1 second before playback starts
        public static final int BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS = 1000; // 1 second after rebuffer
        
        // Memory-Optimized Settings for Low-End Devices
        public static final int LOW_END_MIN_BUFFER_MS = 10000;      // 10 seconds for low-end devices
        public static final int LOW_END_MAX_BUFFER_MS = 15000;      // 15 seconds for low-end devices
        public static final int LOW_END_BUFFER_FOR_PLAYBACK_MS = 500; // 0.5 seconds for low-end devices
        public static final int LOW_END_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS = 500;
    }

    /**
     * 📦 Create Optimized ExoPlayer Instance
     * Configures ExoPlayer with memory-efficient settings
     */
    public static ExoPlayer createOptimizedPlayer(Context context) {
        return createOptimizedPlayer(context, false);
    }

    /**
     * 📦 Create Optimized ExoPlayer Instance with Device Detection
     * @param context Application context
     * @param isLowEndDevice Whether to use low-end device optimizations
     */
    public static ExoPlayer createOptimizedPlayer(Context context, boolean isLowEndDevice) {
        // ⚙️ Configure Load Control (Buffer Management)
        LoadControl loadControl = createOptimizedLoadControl(isLowEndDevice);
        
        // 🧩 Configure Renderers Factory (Asynchronous Queueing)
        RenderersFactory renderersFactory = createOptimizedRenderersFactory(context);
        
        // 📊 Configure Track Selector (Adaptive Streaming)
        TrackSelector trackSelector = createOptimizedTrackSelector(context);

        // 📦 Build ExoPlayer with Optimized Configuration
        return new ExoPlayer.Builder(context)
                .setLoadControl(loadControl)
                .setRenderersFactory(renderersFactory)
                .setTrackSelector(trackSelector)
                .build();
    }

    /**
     * ⚙️ Create Optimized Load Control
     * Implements buffer settings to prevent memory overload
     */
    private static LoadControl createOptimizedLoadControl(boolean isLowEndDevice) {
        if (isLowEndDevice) {
            // Memory-optimized settings for low-end devices
            return new DefaultLoadControl.Builder()
                    .setBufferDurationsMs(
                            BufferConfig.LOW_END_MIN_BUFFER_MS,
                            BufferConfig.LOW_END_MAX_BUFFER_MS,
                            BufferConfig.LOW_END_BUFFER_FOR_PLAYBACK_MS,
                            BufferConfig.LOW_END_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS
                    )
                    .build();
        } else {
            // Standard optimized settings (Drip-Feeding technique)
            return new DefaultLoadControl.Builder()
                    .setBufferDurationsMs(
                            BufferConfig.MIN_BUFFER_MS,
                            BufferConfig.MAX_BUFFER_MS,
                            BufferConfig.BUFFER_FOR_PLAYBACK_MS,
                            BufferConfig.BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS
                    )
                    .build();
        }
    }

    /**
     * 🧩 Create Optimized Renderers Factory
     * Enables asynchronous buffer queueing for Android 6.0+ devices
     */
    private static RenderersFactory createOptimizedRenderersFactory(Context context) {
        DefaultRenderersFactory renderersFactory = new DefaultRenderersFactory(context);
        
        // 🧩 Enable Asynchronous Buffer Queueing for Android 6.0+ (API 23+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            renderersFactory.forceEnableMediaCodecAsynchronousQueueing();
        }
        
        return renderersFactory;
    }

    /**
     * 📊 Create Optimized Track Selector
     * Implements adaptive streaming for memory efficiency
     */
    private static TrackSelector createOptimizedTrackSelector(Context context) {
        // Create bandwidth meter for adaptive streaming
        DefaultBandwidthMeter bandwidthMeter = new DefaultBandwidthMeter.Builder(context).build();

        // Create adaptive track selection factory
        AdaptiveTrackSelection.Factory trackSelectionFactory = new AdaptiveTrackSelection.Factory();

        // Create track selector with adaptive capabilities
        DefaultTrackSelector trackSelector = new DefaultTrackSelector(context, trackSelectionFactory);
        
        // Configure track selector for memory efficiency
        DefaultTrackSelector.Parameters trackSelectorParameters = trackSelector.getParameters()
                .buildUpon()
                .setMaxVideoSizeSd() // Limit to SD quality for memory efficiency
                .setForceLowestBitrate(false) // Allow adaptive quality
                .build();
        
        trackSelector.setParameters(trackSelectorParameters);
        
        return trackSelector;
    }

    /**
     * 🧠 Device Memory Detection
     * Determines if device should use low-end optimizations
     */
    public static boolean isLowEndDevice(Context context) {
        android.app.ActivityManager activityManager = 
            (android.app.ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        
        if (activityManager != null) {
            int memoryClass = activityManager.getMemoryClass(); // in MB
            return memoryClass <= 128; // Consider devices with 128MB or less as low-end
        }
        
        return false; // Default to false if detection fails
    }

    /**
     * 🧼 Player Lifecycle Management
     * Utility methods for proper resource management
     */
    public static class LifecycleManager {
        
        /**
         * Release ExoPlayer resources safely
         */
        public static void releasePlayer(ExoPlayer player) {
            if (player != null) {
                try {
                    player.stop();
                    player.clearMediaItems();
                    player.release();
                } catch (Exception e) {
                    android.util.Log.w("ExoPlayerConfig", "Error releasing player: " + e.getMessage());
                }
            }
        }
        
        /**
         * Pause ExoPlayer safely
         */
        public static void pausePlayer(ExoPlayer player) {
            if (player != null && player.isPlaying()) {
                try {
                    player.pause();
                } catch (Exception e) {
                    android.util.Log.w("ExoPlayerConfig", "Error pausing player: " + e.getMessage());
                }
            }
        }
        
        /**
         * Resume ExoPlayer safely
         */
        public static void resumePlayer(ExoPlayer player) {
            if (player != null && !player.isPlaying()) {
                try {
                    player.play();
                } catch (Exception e) {
                    android.util.Log.w("ExoPlayerConfig", "Error resuming player: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 📊 Memory Usage Monitoring
     * Utility methods for tracking memory consumption
     */
    public static class MemoryMonitor {
        
        /**
         * Get current memory usage in MB
         */
        public static long getCurrentMemoryUsage() {
            Runtime runtime = Runtime.getRuntime();
            return (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024);
        }
        
        /**
         * Get available memory in MB
         */
        public static long getAvailableMemory() {
            Runtime runtime = Runtime.getRuntime();
            return runtime.freeMemory() / (1024 * 1024);
        }
        
        /**
         * Check if memory usage is critical
         */
        public static boolean isMemoryUsageCritical() {
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            long maxMemory = runtime.maxMemory();
            
            // Consider critical if using more than 80% of available memory
            return (usedMemory * 100 / maxMemory) > 80;
        }
    }
}
