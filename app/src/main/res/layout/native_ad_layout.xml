<?xml version="1.0" encoding="utf-8"?>
<!-- ✅ FRESH NATIVE AD LAYOUT - Simple and Working -->
<com.google.android.gms.ads.nativead.NativeAdView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_ad_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:background="#FFFFFF"
    android:elevation="2dp"
    android:padding="12dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="100dp">

        <!-- Ad Attribution Label -->
        <TextView
            android:id="@+id/ad_attribution"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:background="#E0E0E0"
            android:padding="4dp"
            android:text="Ad"
            android:textColor="#666666"
            android:textSize="10sp"
            android:textStyle="bold" />

        <!-- App Icon -->
        <ImageView
            android:id="@+id/ad_app_icon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="12dp"
            android:contentDescription="App Icon"
            android:scaleType="centerCrop" />

        <!-- Content Area -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/ad_app_icon"
            android:layout_toStartOf="@id/ad_call_to_action"
            android:layout_marginEnd="12dp"
            android:orientation="vertical">

            <!-- Headline -->
            <TextView
                android:id="@+id/ad_headline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="#000000"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Body Text -->
            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="#666666"
                android:textSize="14sp"
                android:visibility="gone" />

            <!-- Advertiser -->
            <TextView
                android:id="@+id/ad_advertiser"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="#888888"
                android:textSize="12sp"
                android:visibility="gone" />

            <!-- Star Rating -->
            <RatingBar
                android:id="@+id/ad_stars"
                style="?android:attr/ratingBarStyleSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:isIndicator="true"
                android:numStars="5"
                android:stepSize="0.5"
                android:visibility="gone" />

            <!-- Price -->
            <TextView
                android:id="@+id/ad_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="#1E88E5"
                android:textSize="14sp"
                android:textStyle="bold"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Call to Action Button -->
        <Button
            android:id="@+id/ad_call_to_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:backgroundTint="#1E88E5"
            android:minWidth="80dp"
            android:padding="8dp"
            android:text="Install"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:textStyle="bold" />

    </RelativeLayout>

</com.google.android.gms.ads.nativead.NativeAdView>
