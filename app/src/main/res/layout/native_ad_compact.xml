<?xml version="1.0" encoding="utf-8"?>
<!-- ✅ BEST PRACTICE: Scalable Native Ad Layout -->
<com.google.android.gms.ads.nativead.NativeAdView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_ad_view"
    android:layout_width="match_parent"
    android:layout_height="140dp"
    android:layout_margin="8dp"
    android:background="@drawable/ad_background"
    android:padding="12dp">

    <!-- ✅ AD ATTRIBUTION - Required by Google Policy -->
    <TextView
        android:id="@+id/ad_attribution"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Ad"
        android:textColor="#666666"
        android:background="#E0E0E0"
        android:padding="4dp"
        android:textSize="10sp"
        android:textStyle="bold"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true" />

    <!-- ✅ App Icon - Fixed size for consistency -->
    <ImageView
        android:id="@+id/ad_app_icon"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:scaleType="centerCrop"
        android:contentDescription="Ad App Icon" />

    <!-- ✅ Content Section - Responsive width -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/ad_app_icon"
        android:layout_toStartOf="@id/ad_call_to_action"
        android:layout_centerVertical="true"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:orientation="vertical">

        <!-- Headline -->
        <TextView
            android:id="@+id/ad_headline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#000000"
            android:textSize="16sp"
            android:textStyle="bold"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- Body Text -->
        <TextView
            android:id="@+id/ad_body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#666666"
            android:textSize="14sp"
            android:layout_marginTop="4dp"
            android:maxLines="2"
            android:ellipsize="end"
            android:visibility="gone" />

        <!-- Advertiser -->
        <TextView
            android:id="@+id/ad_advertiser"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#888888"
            android:textSize="12sp"
            android:layout_marginTop="2dp"
            android:visibility="gone" />

        <!-- Star Rating -->
        <RatingBar
            android:id="@+id/ad_stars"
            style="?android:attr/ratingBarStyleSmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:isIndicator="true"
            android:numStars="5"
            android:stepSize="0.5"
            android:visibility="gone" />

        <!-- Price -->
        <TextView
            android:id="@+id/ad_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#1E88E5"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginTop="2dp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- ✅ Call to Action Button - Prominent placement -->
    <Button
        android:id="@+id/ad_call_to_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:textColor="#FFFFFF"
        android:backgroundTint="#1E88E5"
        android:textSize="14sp"
        android:textStyle="bold"
        android:minWidth="80dp"
        android:padding="8dp"
        android:text="Install" />

</com.google.android.gms.ads.nativead.NativeAdView>
