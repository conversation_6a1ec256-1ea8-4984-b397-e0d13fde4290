<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Grid Configuration -->
    <integer name="grid_span_count_portrait">2</integer>
    <integer name="grid_span_count_landscape">3</integer>

    <!-- Basic App Strings -->
    <string name="app_name">SavePro – Status Downloader</string>
    <string name="app_subtitle">WhatsApp Status Downloader</string>

    <!-- AdMob Ad Unit IDs - Production -->
    <string name="interstitial_ad_unit_id">ca-app-pub-7557152164205920/6418571631</string>
    <string name="native_ad_unit_id">ca-app-pub-7557152164205920/5890575132</string>
    
    <!-- Media Grid Strings -->
    <string name="media_type_icon">Media type icon</string>
    <string name="play_video">Play video</string>
    <string name="media_thumbnail">Media Thumbnail</string>
    <string name="download_button">Download Button</string>
    <string name="share_button">Share Button</string>

    <!-- Tab Titles -->
    <string name="images_tab_title">Images</string>
    <string name="videos_tab_title">Videos</string>
    <string name="saved_tab_title">Saved</string>

    <!-- Preview Activity Strings -->
    <string name="back_button">Back</string>
    <string name="image_preview">Image Preview</string>
    <string name="video_preview">Video Preview</string>
    <string name="download">Download</string>
    <string name="share">Share</string>

    <!-- Permission Strings -->
    <string name="not_now">Not Now</string>



    <!-- Tab Fragment Strings -->
    <string name="no_images_found">No Image Statuses Found.</string>
    <string name="no_videos_found">No Video Statuses Found.</string>
    <string name="no_saved_media">No Saved Media Found.</string>
    <string name="no_media_found">No Media Found.</string>

    <!-- Permissions -->
    <string name="permission_needed_title">Media Access Permission</string>
    <string name="permission_denied_message">Media permissions are required to access and save statuses. Please grant them in settings.</string>
    <string name="settings">Settings</string>
    <string name="cancel">Cancel</string>
    <string name="open_settings">Open Settings</string>



    <!-- General -->
    <string name="download_failed">Download failed</string>
    <string name="status_saved_to">Status saved to: %1$s</string>

    <!-- Unified Access Permission Bottom Sheet -->
    <string name="access_permission_title">Media Access Permission</string>
    <string name="access_permission_message">This permission allows the app to access your media files so you can view and save statuses securely.</string>
    <string name="access_permission_button_allow">Grant Access</string>
</resources>
